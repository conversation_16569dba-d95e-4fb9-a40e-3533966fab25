---
title: Agent API
sidebarTitle: Getting Started
description: A robust, production-ready application for serving Agents as an API.
---

Welcome to the Simple Agent API: a robust, production-ready application for serving Agents as an API. It includes:

- A FastAPI server for handling API requests.
- A PostgreSQL database for storing Agent sessions, knowledge, and memories.
- A set of pre-built Agents to use as a starting point.


<Snippet file="simple-agent-api-setup.mdx" />

<Snippet file="create-simple-agent-api-codebase.mdx" />


<Snippet file="simple-agent-api-dependency-management.mdx" />

<Snippet file="simple-agent-api-production.mdx" />

## Additional Information

Congratulations on running your  Agent API.

- Read how to [use workspaces with your Agent API](/workspaces/introduction)