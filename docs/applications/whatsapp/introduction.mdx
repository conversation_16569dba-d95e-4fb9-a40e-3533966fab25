---
title: Whatsapp App
description: Host agents as Whatsapp Applications.
---

The Whatsapp App is used to serve Agents or Teams interacting via WhatsApp, using a FastAPI server to handle webhook events and to send messages.

<Snippet file="setup-whatsapp-app.mdx" />

### Example Usage

Create an agent, wrap it with `WhatsappAPI`, and serve it:

```python
from agno.agent import Agent
from agno.app.whatsapp.app import WhatsappAPI
from agno.models.openai import OpenAIChat
from agno.tools.openai import OpenAITools

image_agent = Agent(
    model=OpenAIChat(id="gpt-4o"), # Ensure OPENAI_API_KEY is set
    tools=[OpenAITools(image_model="gpt-image-1")],
    markdown=True,
    show_tool_calls=True,
    debug_mode=True,
    add_history_to_messages=True,
)

# Async router by default (use_async=True)
whatsapp_app = WhatsappAPI(
    agent=image_agent,
    name="Image Generation Tools",
    app_id="image_generation_tools",
    description="A tool that generates images using the OpenAI API.",
)

app = whatsapp_app.get_app()

if __name__ == "__main__":
    whatsapp_app.serve(app="image_generation_tools:app", port=8000, reload=True)
```

**To run:**
1.  Ensure `OPENAI_API_KEY` environment variable is set if using OpenAI models.
2.  The API will be running (e.g., `http://localhost:8000`), but interaction is primarily via WhatsApp through the configured webhook.
3.  API docs (if enabled in settings) might be at `http://localhost:8000/docs`.



## Core Components

- `WhatsappAPI`: Wraps Agno agents/teams for WhatsApp integration via FastAPI.
- `WhatsappAPI.serve`: Serves the FastAPI app using Uvicorn, configured for WhatsApp.

## `WhatsappAPI` Class

Main entry point for Agno WhatsApp applications.

### Initialization Parameters

| Parameter  | Type                      | Default | Description                                           |
| ---------- | ------------------------- | ------- | ----------------------------------------------------- |
| `agent`    | `Optional[Agent]`         | `None`  | Agno `Agent` instance.                                |
| `team`     | `Optional[Team]`          | `None`  | Agno `Team` instance.                                 |
| `settings` | `Optional[APIAppSettings]`| `None`  | API configuration. Defaults if `None`.                |
| `api_app`  | `Optional[FastAPI]`       | `None`  | Existing FastAPI app. New one created if `None`.    |
| `router`   | `Optional[APIRouter]`     | `None`  | Existing APIRouter. New one created if `None`.        |
| `app_id`    | `Optional[str]`           | `None`  | App identifier (autogenerated if not set).                         |
| `name`    | `Optional[str]`           | `None`  | Name for the App.                         |
| `description`    | `Optional[str]`           | `None`  | Description for the App.                         |

*Provide `agent` or `team`, not both.*

### Key Method

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `get_app` | `use_async: bool = True`<br/>`prefix: str = ""` | `FastAPI` | Returns configured FastAPI app. Sets prefix, error handlers, and includes WhatsApp routers. Async router is used by default. |

## Endpoints

Endpoints are accessible at the `prefix` (default is root level: `""`).

### 1. `GET /webhook`
    *   **Description**: Verifies WhatsApp webhook (challenge).
    *   **Responses**:
        *   `200 OK`: Returns `hub.challenge` if tokens match.
        *   `403 Forbidden`: Token mismatch or invalid mode.
        *   `500 Internal Server Error`: `WHATSAPP_VERIFY_TOKEN` not set.

### 2. `POST /webhook`
    *   **Description**: Receives incoming WhatsApp messages and events.
    *   **Processing**:
        *   Validates signature (if `APP_ENV="production"` and `WHATSAPP_APP_SECRET` is set).
        *   Processes messages (text, image, video, audio, document) via `agent.arun()` or `team.arun()`.
        *   Sends replies via WhatsApp.
    *   **Responses**:
        *   `200 OK`: `{"status": "processing"}` or `{"status": "ignored"}`.
        *   `403 Forbidden`: Invalid signature.
        *   `500 Internal Server Error`: Other processing errors.

### Parameters

| Parameter | Type                | Default     | Description                                        |
| --------- | ------------------- | ----------- | -------------------------------------------------- |
| `app`     | `Union[str, FastAPI]` | `N/A`       | FastAPI app instance or import string (Required). |
| `host`    | `str`               | `"localhost"` | Host to bind.                                      |
| `port`    | `int`               | `7777`      | Port to bind.                                      |
| `reload`  | `bool`              | `False`     | Enable auto-reload for development.                |
