---
title: AG-UI App
description: Expose your Agno Agent as a AG-UI compatible app
---

AG-UI, or [Agent-User Interaction Protocol](https://github.com/ag-ui-protocol/ag-ui), is a protocol standarizing how AI agents connect to front-end applications.


## Example usage

<Steps>
  <Step title="Install the backend dependencies">
    ```bash
    pip install agno ag-ui-protocol
    ```
  </Step>

  <Step title="Run the backend">
    Now let's run a `AGUIApp` exposing an Agno Agent. You can use the previous code!
  </Step>

  <Step title="Run the frontend">


You can use [Dojo](https://github.com/ag-ui-protocol/ag-ui/tree/main/typescript-sdk/apps/dojo), an advanced and customizable option to use as frontend for AG-UI agents.

1. Clone the project: `git clone https://github.com/ag-ui-protocol/ag-ui.git`
2. Follow the instructions [here](https://github.com/ag-ui-protocol/ag-ui/tree/main/typescript-sdk/apps/dojo) to learn how to install the needed dependencies and run the project.
3. Remember to install the dependencies in `/ag-ui/typescript-sdk` with `pnpm install`, and to build the Agno package in `/integrations/agno` with `pnpm run build`.
4. You can now run your Dojo! It will show our Agno agent as one of the available options.
</Step>

<Step title="Chat with your Agno Agent">

Done! If you are running Dojo as your front-end, you can now go to http://localhost:3000 in your browser and chat with your Agno Agent.

</Step>
</Steps>


![AG-UI Dojo screenshot](/images/agui-dojo.png)

You can see more examples in our [AG-UI integration examples](/examples/applications/ag-ui) section.

## Core Components

- `AGUIApp`: Wraps Agno agents/teams for in a FastAPI app.
- `serve`: Serves the FastAPI AG-UI app using Uvicorn.

`AGUIApp` uses helper functions for routing.

## `AGUIApp` Class

Main entry point for Agno AG-UI apps.

### Initialization Parameters

| Parameter  | Type                      | Default | Description                                           |
| ---------- | ------------------------- | ------- | ----------------------------------------------------- |
| `agent`    | `Optional[Agent]`         | `None`  | Agno `Agent` instance.                                |
| `team`     | `Optional[Team]`          | `None`  | Agno `Team` instance.                                 |
| `settings` | `Optional[APIAppSettings]`| `None`  | API configuration. Defaults if `None`.                |
| `api_app`  | `Optional[FastAPI]`       | `None`  | Existing FastAPI app. New one created if `None`.    |
| `router`   | `Optional[APIRouter]`     | `None`  | Existing APIRouter. New one created if `None`.        |
| `app_id`    | `Optional[str]`           | `None`  | App identifier (autogenerated if not set).                         |
| `name`    | `Optional[str]`           | `None`  | Name for the App.                         |
| `description`    | `Optional[str]`           | `None`  |  Description for the App.                         |

*Provide `agent` or `team`, not both.*

### Key Method

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `get_app` | `use_async: bool = True` | `FastAPI` | Returns configured FastAPI app (async by default). Sets prefix, error handlers, CORS, docs. |


## Endpoints

Endpoints are available at the specified `prefix` (default `/v1`).

### 1. `POST /agui`

This is the main entrypoint to interact with your Agno Agent or Team.

It expects a `RunAgentInput` object (from the `ag-ui-protocol` package) as defined by the protocol. You can read more about it in [their docs](https://docs.ag-ui.com/quickstart/server).


## Serving the Application (`serve`)

Serves the FastAPI app using Uvicorn.

### Parameters

| Parameter | Type                | Default     | Description                                        |
| --------- | ------------------- | ----------- | -------------------------------------------------- |
| `app`     | `Union[str, FastAPI]` | `N/A`       | FastAPI app instance or import string (Required). |
| `host`    | `str`               | `"localhost"` | Host to bind.                                      |
| `port`    | `int`               | `7777`      | Port to bind.                                      |
| `reload`  | `bool`              | `False`     | Enable auto-reload for development.                |


You can check some usage examples in our [AG-UI integration examples](/examples/applications/ag-ui) section.

You can also check the [CopilotKit docs](https://docs.copilotkit.ai/agno) on working with Agno, to learn more on how to build the UI side.