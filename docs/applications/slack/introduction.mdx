---
title: Slack App
description: Host agents as Slack Applications.
---

The Slack App is used to serve Agents or Teams via Slack, using a FastAPI server to handle Slack events and send messages.


## Setup Steps

<Snippet file="setup-slack-app.mdx" />

### Example Usage

Create an agent, wrap it with `SlackAPI`, and serve it:

```python
from agno.agent import Agent
from agno.app.slack.app import SlackAPI
from agno.models.openai import OpenAIChat

basic_agent = Agent(
    name="Basic Agent",
    model=OpenAIChat(id="gpt-4o"), # Ensure OPENAI_API_KEY is set
    add_history_to_messages=True,
    num_history_responses=3,
    add_datetime_to_instructions=True,
)

slack_api_app = SlackAPI(
    agent=basic_agent,
)
app = slack_api_app.get_app()

if __name__ == "__main__":
    slack_api_app.serve("basic:app", port=8000, reload=True)
```

## Core Components

- `SlackAPI`: Wraps Agno agents/teams for Slack integration via FastAPI.
- `SlackAPI.serve`: Serves the FastAPI app using Uvicorn, configured for Slack.

## `SlackAPI` Class

Main entry point for Agno Slack applications.

### Initialization Parameters

| Parameter  | Type                      | Default | Description                                           |
| ---------- | ------------------------- | ------- | ----------------------------------------------------- |
| `agent`    | `Optional[Agent]`         | `None`  | Agno `Agent` instance.                                |
| `team`     | `Optional[Team]`          | `None`  | Agno `Team` instance.                                 |
| `settings` | `Optional[APIAppSettings]`| `None`  | API configuration. Defaults if `None`.                |
| `api_app`  | `Optional[FastAPI]`       | `None`  | Existing FastAPI app. New one created if `None`.      |
| `router`   | `Optional[APIRouter]`     | `None`  | Existing APIRouter. New one created if `None`.        |
| `app_id`   | `Optional[str]`           | `None`  | App identifier (autogenerated if not set).            |
| `name`     | `Optional[str]`           | `None`  | Name for the App.                                     |
| `description` | `Optional[str]`         | `None`  | Description for the App.                              |

*Provide `agent` or `team`, not both.*

## Endpoints

The main endpoint for Slack integration:

### `POST /slack/events`
- **Description**: Handles all Slack events including messages and app mentions
- **Security**: Verifies Slack signature for each request
- **Event Types**:
  - URL verification challenges
  - Message events
  - App mention events
- **Features**:
  - Threaded conversations
  - Background task processing
  - Message splitting for long responses
  - Support for both direct messages and channel interactions

## Testing the Integration

1. Start your application locally with `python <my-app>.py` (ensure ngrok is running)
2. Invite the bot to a channel using `/invite @YourAppName`
3. Try mentioning the bot in the channel: `@YourAppName hello`
4. Test direct messages by opening a DM with the bot

## Troubleshooting

- Verify all environment variables are set correctly
- Ensure the bot has proper permissions and is invited to channels
- Check ngrok connection and URL configuration
- Verify event subscriptions are properly configured
- Monitor application logs for detailed error messages

## Support

For additional help or to report issues, please refer to the documentation or open an issue in the repository.
