| Parameter            | Type               | Default | Description                                                |
| ------------------- | ------------------ | ------- | ---------------------------------------------------------- |
| `table_name`        | `str`              | -       | The name of the table to store Workflow sessions.          |
| `db_url`            | `Optional[str]`    | `None`  | The database URL to connect to.                           |
| `db_file`           | `Optional[str]`    | `None`  | The database file to connect to.                          |
| `db_engine`         | `Optional[Engine]` | `None`  | The SQLAlchemy database engine to use.                    |
| `schema_version`    | `int`              | `1`     | Version of the schema.                                    |
| `auto_upgrade_schema` | `bool`           | `False` | Whether to automatically upgrade the schema.               |
