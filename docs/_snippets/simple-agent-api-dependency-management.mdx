
## Managing Python Dependencies

<Steps>

<Step title="Modify pyproject.toml">

Add or update your desired Python package dependencies in the `[tool.poetry.dependencies]` section of the `pyproject.toml` file.

</Step>

<Step title="Generate requirements.txt">

The `requirements.txt` file is used to build the application image. After modifying `pyproject.toml`, regenerate `requirements.txt` using:

```bash
./scripts/generate_requirements.sh
```

To upgrade all existing dependencies to their latest compatible versions, run:

```bash
./scripts/generate_requirements.sh upgrade
```

</Step>

<Step title="Rebuild Docker Images">

Rebuild your Docker images to include the updated dependencies:

```bash
docker compose up -d --build
```
</Step>

</Steps>