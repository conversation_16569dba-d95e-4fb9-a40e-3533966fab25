| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `collection_name` | `str` | Name of the MongoDB collection | Required |
| `db_url` | `Optional[str]` | MongoDB connection string | `"mongodb://localhost:27017/"` |
| `database` | `str` | Database name | `"agno"` |
| `embedder` | `Optional[Embedder]` | Embedder instance for generating embeddings | `OpenAIEmbedder()` |
| `distance_metric` | `str` | Distance metric for similarity | `Distance.cosine` |
| `overwrite` | `bool` | Overwrite existing collection and index if True | `False` |
| `wait_until_index_ready` | `Optional[float]` | Time in seconds to wait until the index is ready | `None` |
| `wait_after_insert` | `Optional[float]` | Time in seconds to wait after inserting documents | `None` |
| `max_pool_size` | `int` | Maximum number of connections in the connection pool | `100` |
| `retry_writes` | `bool` | Whether to retry write operations | `True` |
| `client` | `Optional[MongoClient]` | An existing MongoClient instance | `None` |