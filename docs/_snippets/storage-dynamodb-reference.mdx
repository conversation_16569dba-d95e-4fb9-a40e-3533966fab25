### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `table_name` | `str` | Name of the DynamoDB table | Required |
| `region_name` | `Optional[str]` | AWS region name | `None` |
| `aws_access_key_id` | `Optional[str]` | AWS access key ID | `None` |
| `aws_secret_access_key` | `Optional[str]` | AWS secret access key | `None` |
| `endpoint_url` | `Optional[str]` | Custom endpoint URL | `None` |
| `create_table_if_not_exists` | `bool` | Auto-create table if missing | `True` |