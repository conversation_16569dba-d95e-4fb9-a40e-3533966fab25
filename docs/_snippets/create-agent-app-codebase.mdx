## Create your Agent App codebase

Create your codebase using the `agent-app` template, give it any name you like.

<CodeGroup>

```bash Mac
ag ws create --template agent-app --name agent-app
```

```bash Windows
ag ws create --template agent-app --name agent-app
```

</CodeGroup>

This will create a folder named `agent-app` with the following structure:

```bash
agent-app                     # root directory
├── agents                  # your Agents go here
├── api                     # your Api routes go here
├── ui                      # your Streamlit apps go here
├── db                      # your database tables go here
├── Dockerfile              # Dockerfile for the application
├── pyproject.toml          # python project definition
├── requirements.txt        # python dependencies generated using pyproject.toml
├── scripts                 # helper scripts
├── utils                   # shared utilities
└── workspace               # Agno workspace directory
    ├── dev_resources.py    # dev resources running locally
    ├── prd_resources.py    # production resources running on AWS
    ├── secrets             # secrets
    └── settings.py         # Agno workspace settings
```
