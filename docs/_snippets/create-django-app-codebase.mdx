## Create your codebase

Create your codebase using the `django-app` template pre-configured with [Django](https://www.djangoproject.com/) and [PostgreSQL](https://www.postgresql.org/)

<CodeGroup>

```bash Mac
ag ws create -t django-app -n django-app
```

```bash Windows
ag ws create -t django-app -n django-app
```

</CodeGroup>

This will create a folder named `django-app` with the following structure:

```bash
django-app                  # root directory for your django-app
├── app                   # directory for the Jjango project
├── nginx                 # directory for nginx used in production for static files
├── manage.py             # django-admin file
├── dev.Dockerfile        # Dockerfile for the dev application
├── prd.Dockerfile        # Dockerfile for the production application
├── pyproject.toml        # python project definition
├── requirements.txt      # python dependencies generated by pyproject.toml
├── scripts               # directory for helper scripts
├── tests                 # directory for unit tests
└── workspace             # agno workspace directory
    ├── dev_resources.py  # dev resources running locally
    ├── prd_resources.py  # production resources running on AWS
    ├── secrets           # directory for storing secrets
    └── settings.py       # agno workspace settings
```
