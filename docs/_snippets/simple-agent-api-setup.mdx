
## Quickstart

Follow these steps to get your Agent API up and running:

**Prerequisites**: Docker Desktop should be installed and running.


<Steps>
  <Step title="Clone the repo">
    ```bash 
    git clone https://github.com/agno-agi/agent-api.git
    cd agent-api
    ```
  </Step>

  <Step title="Export your OpenAI key">

    <CodeGroup>
    ```bash Mac
    export OPENAI_API_KEY=sk-***
    ```

    ```bash Windows
    setx OPENAI_API_KEY sk-***
    ```
    </CodeGroup>

  </Step>

  <Step title="Start the application">
    ```bash
    docker compose up -d
    ```
  </Step>

  <Step title="Test the application">

  This command starts:
  
  - The FastAPI server, running on [`localhost:8000`](http://localhost:8000).
  - The PostgreSQL database, accessible on `localhost:5432`.
  
  Once started, you can:
  
  - Test the API at [localhost:8000/docs](http://localhost:8000/docs).
  - Connect to Agno Playground or Agent UI:
      - Open the Agno Playground [app.agno.com/playground/agents](https://app.agno.com/playground/agents).
      - Add `http://localhost:8000` as a new endpoint. You can name it `Agent API` (or any name you prefer).
      - Select your newly added endpoint and start chatting with your Agents.

  </Step>

  <Step title="Stop the application">  
    ```bash
    docker compose down
    ```
  </Step>

</Steps>
