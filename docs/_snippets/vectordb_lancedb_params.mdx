| Parameter     | Type           | Default | Description                                                                                                            |
| ------------- | -------------- | ------- | ---------------------------------------------------------------------------------------------------------------------- |
| `uri`         | `str`          | -       | The URI to connect to.                                                                                                 |
| `table`       | `LanceTable`   | -       | The Lance table to use.                                                                                                |
| `table_name`  | `str`          | -       | The name of the table to use.                                                                                          |
| `connection`  | `DBConnection` | -       | The database connection to use.                                                                                        |
| `api_key`     | `str`          | -       | The API key to use.                                                                                                    |
| `embedder`    | `Embedder`     | -       | The embedder to use.                                                                                                   |
| `search_type` | `SearchType`   | vector  | The search type to use.                                                                                                |
| `distance`    | `Distance`     | cosine  | The distance to use.                                                                                                   |
| `nprobes`     | `int`          | -       | The number of probes to use. [More Info](https://lancedb.github.io/lancedb/ann_indexes/#use-gpu-to-build-vector-index) |
| `reranker`    | `Reranker`     | -       | The reranker to use. [More Info](https://lancedb.github.io/lancedb/hybrid_search/eval/)                                |
| `use_tantivy` | `bool`         | -       | Whether to use tantivy.                                                                                                |
