| Name                          | Type                              | Default              | Description                                                                           |
| ----------------------------- | --------------------------------- | -------------------- | ------------------------------------------------------------------------------------- |
| `id`                          | `str`                             | `"gpt-4o"`           | The id of the OpenAI model to use.                                                    |
| `name`                        | `str`                             | `"OpenAIResponses"`  | The name of this response model instance.                                             |
| `provider`                    | `str`                             | `"OpenAI"`           | The provider of the model.                                                            |
| `include`                     | `Optional[List[str]]`             | `None`               | List of response components to include in the response.                               |
| `max_output_tokens`           | `Optional[int]`                   | `None`               | The maximum number of tokens to generate in the response output.                      |
| `metadata`                    | `Optional[Dict[str, Any]]`        | `None`               | Additional metadata to include with the request.                                      |
| `parallel_tool_calls`         | `Optional[bool]`                  | `None`               | Whether to allow parallel tool calls.                                                 |
| `reasoning`                   | `Optional[Dict[str, Any]]`        | `None`               | Parameters for enabling and controlling reasoning/thinking in the response.           |
| `store`                       | `Optional[bool]`                  | `None`               | Whether to store the output of this response request for model distillation or evals. |
| `temperature`                 | `Optional[float]`                 | `None`               | Controls randomness in the model's output.                                            |
| `top_p`                       | `Optional[float]`                 | `None`               | Controls diversity via nucleus sampling.                                              |
| `truncation`                  | `Optional[str]`                   | `None`               | How to handle content that exceeds the token limit.                                   |
| `user`                        | `Optional[str]`                   | `None`               | A unique identifier representing your end-user.                                       |
| `response_format`             | `Optional[Any]`                   | `None`               | An object specifying the format that the model must output.                           |
| `request_params`              | `Optional[Dict[str, Any]]`        | `None`               | Additional parameters to include in the request.                                      |
| `api_key`                     | `Optional[str]`                   | `None`               | The API key for authenticating with OpenAI.                                           |
| `organization`                | `Optional[str]`                   | `None`               | The organization to use for API requests.                                             |
| `base_url`                    | `Optional[Union[str, httpx.URL]]` | `None`               | The base URL for API requests.                                                        |
| `timeout`                     | `Optional[float]`                 | `None`               | The timeout for API requests.                                                         |
| `max_retries`                 | `Optional[int]`                   | `None`               | The maximum number of retries for failed requests.                                    |
| `default_headers`             | `Optional[Dict[str, str]]`        | `None`               | Default headers to include in all requests.                                           |
| `default_query`               | `Optional[Dict[str, str]]`        | `None`               | Default query parameters to include in all requests.                                  |
| `http_client`                 | `Optional[httpx.Client]`          | `None`               | An optional pre-configured HTTP client.                                               |
| `client_params`               | `Optional[Dict[str, Any]]`        | `None`               | Additional parameters for client configuration.                                       |
| `vector_store_name`           | `str`                             | `"knowledge_base"`   | The name of the vector store for file uploads and retrieval.                          |
