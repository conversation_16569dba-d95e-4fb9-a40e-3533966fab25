| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `uri` | `lancedb.URI` | `"/tmp/lancedb"` | URI path or connection string for LanceDB |
| `table` | `Optional[lancedb.db.LanceTable]` | `None` | Pre-configured LanceDB table instance |
| `table_name` | `Optional[str]` | `None` | Name of the table to store vectors and metadata |
| `connection` | `Optional[lancedb.LanceDBConnection]` | `None` | Pre-configured LanceDB connection |
| `api_key` | `Optional[str]` | `None` | API key for cloud LanceDB authentication |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `search_type` | `SearchType` | `SearchType.vector` | Type of search to perform (vector, keyword, or hybrid) |
| `distance` | `Distance` | `Distance.cosine` | Distance metric to use for similarity search |
| `nprobes` | `Optional[int]` | `None` | Number of probes for approximate nearest neighbor search |
| `reranker` | `Optional[Reranker]` | `None` | Optional reranker instance to rerank search results |
| `use_tantivy` | `bool` | `True` | Whether to use Tantivy for full-text search indexing |
