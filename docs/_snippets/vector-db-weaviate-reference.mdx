| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `wcd_url` | `Optional[str]` | `None` | URL for Weaviate Cloud Deployment. Falls back to `WCD_URL` environment variable if not provided. |
| `wcd_api_key` | `Optional[str]` | `None` | API key for Weaviate Cloud Deployment. Falls back to `WCD_API_KEY` environment variable if not provided. |
| `client` | `Optional[weaviate.WeaviateClient]` | `None` | Pre-configured Weaviate client instance. |
| `local` | `bool` | `False` | Whether to use a local Weaviate instance instead of cloud. |
| `collection` | `str` | `"default"` | Name of the collection to use in Weaviate. |
| `vector_index` | `VectorIndex` | `VectorIndex.HNSW` | Type of vector index to use (HNSW, FLAT, or DYNAMIC). |
| `distance` | `Distance` | `Distance.COSINE` | Distance metric for vector similarity (COSINE, DOT, etc.). |
| `embedder` | `Optional[Embedder]` | `None` | Embedder to use for generating vector embeddings. Defaults to OpenAIEmbedder if not provided. |
| `search_type` | `SearchType` | `SearchType.vector` | Type of search to perform (vector, keyword, or hybrid). |
| `reranker` | `Optional[Reranker]` | `None` | Optional reranker to improve search results. |
| `hybrid_search_alpha` | `float` | `0.5` | Weight between vector and keyword search for hybrid search (0.0 = keyword only, 1.0 = vector only). |
