| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `api_key` | `Optional[str]` | `None` | Firecrawl API key for authentication |
| `params` | `Optional[Dict]` | `None` | Additional parameters to pass to the Firecrawl API |
| `mode` | `Literal["scrape", "crawl"]` | `"scrape"` | Mode of operation - "scrape" for single page, "crawl" for multiple pages |
| `url` | `str` | Required | URL of the website to scrape or crawl |
