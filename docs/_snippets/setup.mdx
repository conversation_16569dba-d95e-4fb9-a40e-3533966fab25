## Setup

<Steps>
  <Step title="Create and activate a virtual environment">

    <CodeGroup>
    ```bash Mac
    python3 -m venv .venv
    source .venv/bin/activate
    ```

    ```bash Windows
    python3 -m venv aienv
    aienv/scripts/activate
    ```
    </CodeGroup>

  </Step>
  <Step title="Install Agno">

    <CodeGroup>
    ```bash Mac
    pip install -U "agno[aws]"
    ```

    ```bash Windows
    pip install -U "agno[aws]"
    ```
    </CodeGroup>

  </Step>
  <Step title="Install uv and docker">
    - Install [uv](https://docs.astral.sh/uv/#getting-started) for managing your python environment.

    ```bash
    curl -LsSf https://astral.sh/uv/install.sh | sh
    ```

    - Install [docker desktop](https://docs.docker.com/desktop/install/mac-install/) to run your app locally
  </Step>
  <Step title="Export your OpenAI key">

    <CodeGroup>
    ```bash Mac
    export OPENAI_API_KEY=sk-***
    ```

    ```bash Windows
    setx OPENAI_API_KEY sk-***
    ```
    </CodeGroup>

    <Tip>
    Agno is compatible with any model provider; simply update the agents in the workspace.
    </Tip>
  </Step>
</Steps>
