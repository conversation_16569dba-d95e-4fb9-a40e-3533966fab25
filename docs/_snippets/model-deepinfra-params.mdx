| Parameter  | Type            | Default                                    | Description                                                                                                              |
| ---------- | --------------- | ------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------ |
| `id`       | `str`           | `"meta-llama/Llama-2-70b-chat-hf"`            | The specific model ID used for generating responses.                                                                     |
| `name`     | `str`           | `"DeepInfra"`                                 | The name identifier for the DeepInfra agent.                                                                                |
| `provider` | `str`           | `"DeepInfra" + id`                           | The provider of the model, combining "DeepInfra" with the model ID.                                                         |
| `api_key`  | `Optional[str]` | -                                          | The API key for authenticating requests to the DeepInfra service. Retrieved from the environment variable `DEEPINFRA_API_KEY`. |
| `base_url` | `str`           | `"https://api.deepinfra.com/v1/openai"`    | The base URL for making API requests to the DeepInfra service.                                                              |
