## Run your Agent App locally

`cd` into the `agent-app` folder

```bash
cd agent-app
```

Start your Agent App using the following command:

<CodeGroup>

```bash terminal
ag ws up
```

```bash shorthand
ag ws up dev:docker
```

```bash full options
ag ws up --env dev --infra docker
```

</CodeGroup>

**Press Enter** to confirm and give a few seconds for the image to download (only the first time). Verify container status and view logs on the docker dashboard.

- Open [localhost:8501](http://localhost:8501) to view the streamlit UI.
- Open [localhost:8000/docs](http://localhost:8000/docs) to view the FastAPI routes.

![agent-app-ui](/images/agent-app-ui.png)

Notes:

- The `Agents` are defined in the `agents` folder.
- The streamlit apps are defined in the `ui` folder
- The API routes are defined in the `api` folder.
