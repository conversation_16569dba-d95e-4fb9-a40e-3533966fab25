### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `id` | `str` | The model ID to use for embeddings | `"jinaai/jina-embeddings-v2-base-code"` |
| `api_key` | `Optional[str]` | Huggingface API key | Environment variable `HUGGINGFACE_API_KEY` |
| `client_params` | `Optional[Dict[str, Any]]` | Additional parameters for client initialization | `None` |
| `huggingface_client` | `Optional[InferenceClient]` | Pre-configured Huggingface client | `None` |