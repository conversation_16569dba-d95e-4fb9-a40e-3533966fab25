| Parameter  | Type            | Default                    | Description                                                                                                        |
| ---------- | --------------- | -------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| `id`       | `str`           | `"v0-1.0-md"`              | The specific model ID used for generating responses.                                                               |
| `name`     | `str`           | `"v0"`                    | The name identifier for the v0 agent.                                                                             |
| `provider` | `str`           | `"Vercel"`                 | The provider of the model, combining "v0" with the model ID.                                                      |
| `api_key`  | `Optional[str]` | -                          | The API key for authenticating requests to the v0 service. Retrieved from the environment variable `V0_API_KEY`. |
| `base_url` | `str`           | `"https://api.v0.dev/v1/"` | The base URL for making API requests to the v0 service.                                                           |
