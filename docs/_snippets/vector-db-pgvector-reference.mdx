| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `table_name` | `str` | Required | Name of the table to store vectors and metadata |
| `schema` | `str` | `"ai"` | Database schema name |
| `db_url` | `Optional[str]` | `None` | Database connection URL |
| `db_engine` | `Optional[Engine]` | `None` | SQLAlchemy database engine |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `search_type` | `SearchType` | `SearchType.vector` | Type of search to perform (vector, keyword, or hybrid) |
| `vector_index` | `Union[Ivfflat, HNSW]` | `HNSW()` | Vector index configuration |
| `distance` | `Distance` | `Distance.cosine` | Distance metric for vector comparisons |
| `prefix_match` | `bool` | `False` | Enable prefix matching for full-text search |
| `vector_score_weight` | `float` | `0.5` | Weight for vector similarity in hybrid search |
| `content_language` | `str` | `"english"` | Language for full-text search |
| `schema_version` | `int` | `1` | Version of the database schema |
| `auto_upgrade_schema` | `bool` | `False` | Automatically upgrade schema if True |
| `reranker` | `Optional[Reranker]` | `None` | Reranker instance for post-processing search results |
