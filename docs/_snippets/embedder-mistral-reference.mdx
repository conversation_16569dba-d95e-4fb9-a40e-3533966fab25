### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `id` | `str` | The model ID to use for embeddings | `"mistral-embed"` |
| `dimensions` | `int` | Output dimensions of the embedding | `1024` |
| `request_params` | `Optional[Dict[str, Any]]` | Additional parameters for embedding requests | `None` |
| `api_key` | `Optional[str]` | Mistral API key | Environment variable `MISTRAL_API_KEY` |
| `endpoint` | `Optional[str]` | Custom API endpoint URL | `None` |
| `max_retries` | `Optional[int]` | Maximum number of retry attempts | `None` |
| `timeout` | `Optional[int]` | Request timeout in seconds | `None` |
| `client_params` | `Optional[Dict[str, Any]]` | Additional parameters for client initialization | `None` |
| `mistral_client` | `Optional[Mistral]` | Pre-configured Mistral client | `None` |