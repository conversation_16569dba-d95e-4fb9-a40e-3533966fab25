| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `collection` | `str` | `None` | Name of the collection to store vectors and metadata in ChromaDB |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `distance` | `Distance` | `Distance.cosine` | Distance metric to use for similarity search |
| `path` | `str` | `"tmp/chromadb"` | Path to store ChromaDB data when using persistent client |
| `persistent_client` | `bool` | `False` | Whether to use persistent ChromaDB client |
| `reranker` | `Optional[Reranker]` | `None` | Optional reranker instance to rerank search results |
