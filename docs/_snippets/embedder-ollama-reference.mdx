### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `id` | `str` | The model ID to use for embeddings | `"openhermes"` |
| `dimensions` | `int` | Output dimensions of the embedding | `4096` |
| `host` | `Optional[str]` | Host URL for Ollama server | `None` |
| `timeout` | `Optional[Any]` | Request timeout | `None` |
| `options` | `Optional[Any]` | Additional options for embedding generation | `None` |
| `client_kwargs` | `Optional[Dict[str, Any]]` | Additional parameters for client initialization | `None` |
| `ollama_client` | `Optional[OllamaClient]` | Pre-configured Ollama client | `None` |