| Parameter     | Type                                  | Default                                | Description                                                                                                                              |
|---------------|---------------------------------------|----------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------|
| `name`        | `Optional[str]`                       | `None`                                 | **Workflow name**                                                                                                                        |
| `workflow_id` | `Optional[str]`                       | `None`                                 | **Workflow UUID** (autogenerated if not set)                                                                                             |
| `description` | `Optional[str]`                       | `None`                                 | **Workflow description** (only shown in the UI)                                                                                          |
| `user_id`     | `Optional[str]`                       | `None`                                 | **ID of the user** interacting with this workflow                                                                                        |
| `session_id`  | `Optional[str]`                       | `None`                                 | **Session UUID** (autogenerated if not set)                                                                                              |
| `session_name`| `Optional[str]`                       | `None`                                 | **Session name**                                                                                                                         |
| `session_state`| `Dict[str, Any]`                     | `{}` (empty dict)                      | **Session state** stored in the database                                                                                                 |
| `memory`      | `Optional[WorkflowMemory]`            | `None`                                 | **Workflow Memory**                                                                                                                      |
| `storage`     | `Optional[WorkflowStorage]`           | `None`                                 | **Workflow Storage**                                                                                                                     |
| `extra_data`  | `Optional[Dict[str, Any]]`            | `None`                                 | **Extra data** stored with this workflow                                                                                                 |
| `debug_mode`  | `bool`                                | `False`                                | Enable debug logs                                                                                                                        |
| `monitoring`  | `bool`                                | `False` (env: `AGNO_MONITOR`)          | If `True`, logs Workflow information to agno.com for monitoring. Defaults to `True` if `AGNO_MONITOR="true"` in the environment.         |
| `telemetry`   | `bool`                                | `True` (env: `AGNO_TELEMETRY`)         | If `True`, logs minimal telemetry for analytics. Defaults to `True` if `AGNO_TELEMETRY="true"` in the environment.                       |
| `run_id`      | `Optional[str]`                       | `None`                                 | **(Do not set manually)** Unique ID for each Workflow run                                                                                |
| `run_input`   | `Optional[Dict[str, Any]]`            | `None`                                 | **(Do not set manually)** Input passed to the current run                                                                                |
| `run_response`| `Optional[RunResponse]`               | `None`                                 | **(Do not set manually)** Response generated by the current run                                                                          |
| `images`      | `Optional[List[ImageArtifact]]`       | `None`                                 | **Images generated** during this session                                                                                                 |
| `videos`      | `Optional[List[VideoArtifact]]`       | `None`                                 | **Videos generated** during this session                                                                                                 |
| `audio`       | `Optional[List[AudioArtifact]]`       | `None`                                 | **Audio generated** during this session                                                                                                  |
