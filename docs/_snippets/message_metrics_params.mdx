| Field | Description |
|-------------------------|-----------------------------------------------------------------------------|
| input_tokens | Number of tokens in the prompt/input to the model. |
| output_tokens | Number of tokens generated by the model as output. |
| total_tokens | Total tokens used (input + output). |
| prompt_tokens | Tokens in the prompt (same as input_tokens in the case of OpenAI). |
| completion_tokens | Tokens in the completion  (same as output_tokens in the case of OpenAI). |
| audio_tokens | Total audio tokens (if using audio input/output). |
| input_audio_tokens | Audio tokens in the input. |
| output_audio_tokens | Audio tokens in the output. |
| cached_tokens | Tokens served from cache (if caching is used). |
| cache_write_tokens | Tokens written to cache. |
| reasoning_tokens | Tokens used for reasoning steps (if enabled). |
| prompt_tokens_details | Dict with detailed breakdown of prompt tokens (used by OpenAI). |
| completion_tokens_details | Dict with detailed breakdown of completion tokens (used by OpenAI). |
| additional_metrics | Any extra metrics provided by the model/tool (e.g., latency, cost, etc.). |
| time | Time taken to generate the message (in seconds). |
| time_to_first_token | Time until the first token is generated (in seconds). |
> Note: Not all fields are always present; it depends on the model/tool and the run.