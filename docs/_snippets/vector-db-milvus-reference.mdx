| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `collection` | `str` | `None` | Name of the Milvus collection to store vectors and metadata |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `distance` | `Distance` | `Distance.cosine` | Distance metric to use for similarity search |
| `uri` | `str` | `"http://localhost:19530"` | URI of the Milvus server. Can be a local file path for Milvus Lite, server address for self-hosted Milvus, or Zilliz Cloud endpoint |
| `token` | `Optional[str]` | `None` | Token for authentication. Format as "username:password" for self-hosted Milvus with auth enabled, or API key for Zilliz Cloud |
