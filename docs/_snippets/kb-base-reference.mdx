| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `reader` | `Optional[Reader]` | `None` | Reader to read the documents |
| `vector_db` | `Optional[VectorDb]` | `None` | Vector db to store the knowledge base |
| `num_documents` | `int` | `2` | Number of relevant documents to return on search |
| `optimize_on` | `Optional[int]` | `1000` | Number of documents to optimize the vector db on |
| `driver` | `str` | `"knowledge"` | Driver for the Assistant knowledge |
