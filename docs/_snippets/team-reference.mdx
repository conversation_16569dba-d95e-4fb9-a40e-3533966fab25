## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `members` | `List[Union[Agent, Team]]` | - | List of agents or teams that make up this team |
| `mode` | `Literal["route", "coordinate", "collaborate"]` | `"coordinate"` | Team operating mode |
| `model` | `Optional[Model]` | `None` | Model to use for the team |
| `name` | `Optional[str]` | `None` | Name of the team |
| `team_id` | `Optional[str] | `None` | Team UUID (autogenerated if not set) |
| `parent_team_id` | `Optional[str]` | `None` | If this team is part of a team itself, this is the role of the team |
| `workflow_id` | `Optional[str]` | `None` | The workflow this team belongs to |
| `role` | `Optional[str]` | `None` | Role of the team within its parent team |
| `user_id` | `Optional[str]` | `None` | ID of the user interacting with this team |
| `session_id` | `Optional[str]` | `None` | Session UUID (autogenerated if not set) |
| `team_session_id` | `Optional[str]` | `None` | In the case where the team is a member of a team itself |
| `session_name` | `Optional[str]` | `None` | Session name |
| `session_state` | `Optional[Dict[str, Any]]` | `None` | Session state (stored in the database to persist across runs) |
| `team_session_state` | `Optional[Dict[str, Any]]` | `None` | Team session state (shared between team leaders and team members) |
| `add_state_in_messages` | `bool` | `False` | If True, add the session state variables in the user and system messages |
| `description` | `Optional[str]` | `None` | A description of the team that is added to the start of the system message |
| `instructions` | `Optional[Union[str, List[str], Callable]]` | `None` | List of instructions for the team |
| `expected_output` | `Optional[str]` | `None` | Provide the expected output from the team |
| `additional_context` | `Optional[str]` | `None` | Additional context added to the end of the system message |
| `success_criteria` | `Optional[str]` | `None` | Define the success criteria for the team |
| `markdown` | `bool` | `False` | If markdown=true, add instructions to format the output using markdown |
| `add_datetime_to_instructions` | `bool` | `False` | If True, add the current datetime to the instructions to give the team a sense of time |
| `add_location_to_instructions` | `bool` | `False` | If True, add the current location to the instructions to give the team a sense of location |
| `add_member_tools_to_system_message` | `bool` | `True` | If True, add the tools available to team members to the system message |
| `knowledge` | `Optional[AgentKnowledge]` | `None` | Add a knowledge base to the team |
| `knowledge_filters` | `Optional[Dict[str, Any]]` | `None` | Filters to apply to knowledge base searches |
| `enable_agentic_knowledge_filters` | `Optional[bool]` | `False` | Let the agent choose the knowledge filters |
| `retriever` | `Optional[Callable[..., Optional[List[Dict]]]]` | `None` | Custom retrieval function to get references |
| `references_format` | `Literal["json", "yaml"]` | `"json"` | Format of the references |
| `context` | `Optional[Dict[str, Any]]` | `None` | User provided context |
| `add_context` | `bool` | `False` | If True, add the context to the user prompt |
| `enable_agentic_context` | `bool` | `False` | If True, enable the team agent to update the team context and automatically send the team context to the members |
| `share_member_interactions` | `bool` | `False` | If True, send all previous member interactions to members |
| `get_member_information_tool` | `bool` | `False` | If True, add a tool to get information about the team members |
| `search_knowledge` | `bool` | `True` | Add a tool to search the knowledge base (aka Agentic RAG) |
| `read_team_history` | `bool` | `False` | If True, read the team history |
| `tools` | `Optional[List[Union[Toolkit, Callable, Function, Dict]]]` | `None` | A list of tools provided to the Model |
| `show_tool_calls` | `bool` | `True` | Show tool calls in Team response |
| `tool_call_limit` | `Optional[int]` | `None` | Maximum number of tool calls allowed |
| `tool_choice` | `Optional[Union[str, Dict[str, Any]]]` | `None` | Controls which (if any) tool is called by the team model |
| `tool_hooks` | `Optional[List[Callable]]` | `None` | A list of hooks to be called before and after the tool call |
| `response_model` | `Optional[Type[BaseModel]]` | `None` | Response model for the team response |
| `use_json_mode` | `bool` | `False` | If `response_model` is set, sets the response mode of the model |
| `parse_response` | `bool` | `True` | If True, parse the response |
| `memory` | `Optional[Union[TeamMemory, Memory]]` | `None` | Memory for the team |
| `enable_agentic_memory` | `bool` | `False` | Enable the agent to manage memories of the user |
| `enable_user_memories` | `bool` | `False` | If True, the agent creates/updates user memories at the end of runs |
| `add_memory_references` | `Optional[bool]` | `None` | If True, the agent adds a reference to the user memories in the response |
| `enable_session_summaries` | `bool` | `False` | If True, the agent creates/updates session summaries at the end of runs |
| `add_session_summary_references` | `Optional[bool]` | `None` | If True, the agent adds a reference to the session summaries in the response |
| `add_history_to_messages` | `bool` | `False` | If True, add messages from the chat history to the messages list sent to the Model. |
| `num_history_runs` | `int` | `3` | Number of historical runs to include in the messages |
| `storage` | `Optional[Storage]` | `None` | Storage for the team |
| `extra_data` | `Optional[Dict[str, Any]]` | `None` | Extra data stored with this team |
| `reasoning` | `bool` | `False` | Enable reasoning for the team |
| `reasoning_model` | `Optional[Model]` | `None` | Model to use for reasoning |
| `reasoning_min_steps` | `int` | `1` | Minimum number of reasoning steps |
| `reasoning_max_steps` | `int` | `10` | Maximum number of reasoning steps |
| `stream` | `Optional[bool]` | `None` | Stream the response from the Team |
| `stream_intermediate_steps` | `bool` | `False` | Stream the intermediate steps from the Team |
| `stream_member_events` | `bool` | `True` | Stream the member events from the Team members |
| `store_events` | `bool` | `False` | Store the streaming events on the TeamRunResponse |
| `events_to_skip` | `Optional[List[Union[RunEvent, TeamRunEvent]]]` | `None` | Specify which event types to skip when storing events on the TeamRunResponse |
| `app_id` | `Optional[str]` | `None` | Optional app ID. Indicates this team is part of an app |
| `debug_mode` | `bool` | `False` | Enable debug logs |
| `show_members_responses` | `bool` | `False` | Enable member logs - Sets the debug_mode for team and members |
| `monitoring` | `bool` | `False` | Log team information to agno.com for monitoring |
| `telemetry` | `bool` | `True` | Log minimal telemetry for analytics |

## Functions

| Function | Description |
|----------|-------------|
| `print_response` | Run the team and print the response |
| `run` | Run the team |
| `aprint_response` | Run the team and print the response asynchronously |
| `arun` | Run the team asynchronously |
| `get_session_summary` | Get the session summary for the given session ID and user ID |
| `get_user_memories` | Get the user memories for the given user ID |
| `get_member_information` | Get information about the members of the team |
| `get_team_history` | Get the team chat history |
| `search_knowledge_base` | Search the knowledge base for information |
| `load_session` | Load an existing session from the database or create a new one |
| `rename_session` | Rename the current session |
| `delete_session` | Delete a session |
| `add_image` | Add an image to the team session |
| `add_video` | Add a video to the team session |
| `add_audio` | Add audio to the team session |
| `get_images` | Get all images from the team session |
| `get_videos` | Get all videos from the team session |
| `get_audio` | Get all audio from the team session |

## Team Modes

The team can operate in three different modes:

1. `"route"` - Routes tasks to specific team members
2. `"coordinate"` - Coordinates between team members (default)
3. `"collaborate"` - Enables collaboration between team members

## Knowledge Base Integration

The team supports integration with a knowledge base through the following features:

- `knowledge`: Add a knowledge base to the team
- `knowledge_filters`: Apply filters to knowledge base searches
- `enable_agentic_knowledge_filters`: Let the agent choose the knowledge filters
- `retriever`: Custom retrieval function for references
- `search_knowledge`: Tool to search the knowledge base

## Memory and History

The team supports various memory and history features:

- `memory`: Team memory storage
- `enable_agentic_memory`: Enable agent memory management
- `enable_user_memories`: Create/update user memories at the end of runs
- `enable_session_summaries`: Create/update session summaries at the end of runs
- `add_history_to_messages`: Add messages from the chat history to the messages list sent to the Model.
- `num_history_runs`: Number of historical runs to include

## Tools and Functions

The team can be equipped with various tools and functions:

- `tools`: List of tools provided to the model
- `tool_call_limit`: Maximum number of tool calls
- `tool_choice`: Control which tool is called
- `tool_hooks`: Hooks for tool execution

## Reasoning

The team supports reasoning capabilities:

- `reasoning`: Enable reasoning
- `reasoning_model`: Model for reasoning
- `reasoning_min_steps`: Minimum reasoning steps
- `reasoning_max_steps`: Maximum reasoning steps



