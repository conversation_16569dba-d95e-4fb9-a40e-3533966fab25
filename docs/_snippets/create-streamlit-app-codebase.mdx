## Create your codebase

Create your codebase using the `streamlit-app` template

<CodeGroup>

```bash Mac
ag ws create -t streamlit-app -n streamlit-app
```

```bash Windows
ag ws create -t streamlit-app -n streamlit-app
```

</CodeGroup>

This will create a folder `streamlit-app` with the following structure:

```bash
streamlit-app                 # root directory for your streamlit-app
├── ai                      # directory for AI components
    ├── agents          # AI agents
    ├── knowledge_base.py   # agent knowledge base
    └── storage.py          # agent storage
├── app                     # directory for Streamlit apps
├── db                      # directory for database components
├── Dockerfile              # Dockerfile for the application
├── pyproject.toml          # python project definition
├── requirements.txt        # python dependencies generated by pyproject.toml
├── scripts                 # directory for helper scripts
├── tests                   # directory for unit tests
├── utils                   # directory for shared utilities
└── workspace               # agno workspace directory
    ├── dev_resources.py    # dev resources running locally
    ├── prd_resources.py    # production resources running on AWS
    ├── secrets             # directory for storing secrets
    └── settings.py         # agno workspace settings
```
