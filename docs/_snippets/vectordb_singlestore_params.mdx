| Parameter    | Type               | Default            | Description                                         |
| ------------ | ------------------ | ------------------ | --------------------------------------------------- |
| `collection` | `str`              | -                  | The name of the collection to use.                  |
| `schema`     | `Optional[str]`    | `"ai"`             | The database schema to use.                         |
| `db_url`     | `Optional[str]`    | `None`             | The database connection URL.                        |
| `db_engine`  | `Optional[Engine]` | `None`             | SQLAlchemy engine instance.                         |
| `embedder`   | `Embedder`         | `OpenAIEmbedder()` | The embedder to use for creating vector embeddings. |
| `distance`   | `Distance`         | `Distance.cosine`  | The distance metric to use for similarity search.   |
