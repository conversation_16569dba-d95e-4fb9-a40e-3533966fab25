| Parameter    | Type            | Default                          | Description                                                                                                                      |
| ------------ | --------------- | -------------------------------- | -------------------------------------------------------------------------------------------------------------------------------- |
| `id`         | `str`           | `"gpt-4o-mini"`                  | The specific model ID used for generating responses.                                                                             |
| `name`       | `str`           | `"AIMLApi"`                      | The name identifier for the AI/ML API agent.                                                                                     |
| `provider`   | `str`           | `"AIMLApi:" + id`                | The provider of the model, combining `"AIMLApi"` with the model ID.                                                             |
| `api_key`    | `Optional[str]` | –                                | The API key for authenticating requests to the AI/ML API service. Retrieved from the environment variable `AIMLAPI_API_KEY`.     |
| `base_url`   | `str`           | `"https://api.aimlapi.com/v1"`   | The base URL for making API requests to the AI/ML API service.                                                                  |
| `max_tokens` | `int`           | `4096`                           | The maximum number of tokens to generate in the response.                                                                       |
