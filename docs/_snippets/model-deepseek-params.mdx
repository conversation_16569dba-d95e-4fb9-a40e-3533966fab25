| Parameter  | Type            | Default                      | Description                                                                                                                       |
| ---------- | --------------- | ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| `id`       | `str`           | `"deepseek-chat"`            | The specific model ID used for generating responses.                                                                              |
| `name`     | `str`           | `"DeepSeek"`                 | The name identifier for the DeepSeek model.                                                                                       |
| `provider` | `str`           | `"DeepSeek"`                 | The provider of the model.                                                                                                        |
| `api_key`  | `Optional[str]` | -                            | The API key used for authenticating requests to the DeepSeek service. Retrieved from the environment variable `DEEPSEEK_API_KEY`. |
| `base_url` | `str`           | `"https://api.deepseek.com"` | The base URL for making API requests to the DeepSeek service.                                                                     |
