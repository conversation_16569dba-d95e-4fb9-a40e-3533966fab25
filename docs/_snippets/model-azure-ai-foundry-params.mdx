| Parameter                 | Type                          | Default             | Description                                                                  |
| ------------------------- | ----------------------------- | ------------------- | ---------------------------------------------------------------------------- |
| `id`                      | `str`                         | -                   | The specific model ID used for generating responses. This field is required. |
| `name`                    | `str`                         | `"AzureOpenAI"`     | The name identifier for the agent.                                           |
| `provider`                | `str`                         | `"Azure"`           | The provider of the model.                                                   |
| `api_key`                 | `Optional[str]`               | `"None"`            | The API key for authenticating requests to the Azure OpenAI service.         |
| `api_version`             | `str`                         | `"2024-10-21"`      | The version of the Azure OpenAI API to use.                                  |
| `azure_endpoint`          | `Optional[str]`               | `"None"`            | The endpoint URL for the Azure OpenAI service.                               |
| `client`                  | `Optional[ChatCompletionsClient]` | `None`              | The client for making requests to the Azure OpenAI service. |
| `async_client`            | `Optional[AsyncChatCompletionsClient]` | `None`              | The asynchronous client for making requests to the Azure OpenAI service. |
| `temperature`             | `Optional[float]`             | `None`              | Controls randomness in the model's output. Higher values make output more random. |
| `max_tokens`             | `Optional[int]`               | `None`              | The maximum number of tokens to generate in the response. |
| `frequency_penalty`      | `Optional[float]`             | `None`              | Reduces repetition by penalizing tokens based on their frequency. |
| `presence_penalty`       | `Optional[float]`             | `None`              | Reduces repetition by penalizing tokens that have appeared at all. |
| `top_p`                  | `Optional[float]`             | `None`              | Controls diversity by limiting cumulative probability of tokens considered. |
| `stop`                   | `Optional[Union[str, List[str]]]` | `None`          | Sequences where the model will stop generating further tokens. |
| `seed`                   | `Optional[int]`               | `None`              | Random seed for deterministic outputs. |
| `model_extras`           | `Optional[Dict[str, Any]]`    | `None`              | Additional model-specific parameters. |
| `request_params`         | `Optional[Dict[str, Any]]`    | `None`              | Additional parameters to pass with the request. |
| `timeout`                | `Optional[float]`             | `None`              | Timeout in seconds for API requests. |
| `max_retries`            | `Optional[int]`               | `None`              | Maximum number of retries for failed requests. |
| `http_client`            | `Optional[httpx.Client]`      | `None`              | Custom HTTP client for making requests. |
| `client_params`          | `Optional[Dict[str, Any]]`    | `None`              | Additional parameters for client configuration. |
