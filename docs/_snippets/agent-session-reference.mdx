| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `session_id` | `str` | Required | Session UUID |
| `agent_id` | `Optional[str]` | `None` | ID of the agent that this session is associated with |
| `user_id` | `Optional[str]` | `None` | ID of the user interacting with this agent |
| `team_session_id` | `Optional[str]` | `None` | ID of the team session that this session is possibly associated with |
| `memory` | `Optional[Dict[str, Any]]` | `None` | Agent Memory |
| `agent_data` | `Optional[Dict[str, Any]]` | `None` | Agent Data: agent_id, name and model |
| `session_data` | `Optional[Dict[str, Any]]` | `None` | Session Data: session_name, session_state, images, videos, audio |
| `extra_data` | `Optional[Dict[str, Any]]` | `None` | Extra Data stored with this agent |
| `created_at` | `Optional[int]` | `None` | The unix timestamp when this session was created |
| `updated_at` | `Optional[int]` | `None` | The unix timestamp when this session was last updated |
