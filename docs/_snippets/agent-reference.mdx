## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `model` | `Optional[Model]` | `None` | Model to use for this Agent |
| `name` | `Optional[str]` | `None` | Agent name |
| `agent_id` | `Optional[str]` | `None` | Agent UUID (autogenerated if not set) |
| `agent_data` | `Optional[Dict[str, Any]]` | `None` | Metadata associated with this agent |
| `introduction` | `Optional[str]` | `None` | Agent introduction. This is added to the chat history when a run is started. |
| `user_id` | `Optional[str]` | `None` | ID of the user interacting with this agent |
| `user_data` | `Optional[Dict[str, Any]]` | `None` | Metadata associated with the user interacting with this agent |
| `session_id` | `Optional[str]` | `None` | Session UUID (autogenerated if not set) |
| `session_name` | `Optional[str]` | `None` | Session name |
| `session_state` | `Optional[Dict[str, Any]]` | `None` | Session state (stored in the database to persist across runs) |
| `context` | `Optional[Dict[str, Any]]` | `None` | Context available for tools and prompt functions |
| `add_context` | `bool` | `False` | If True, add the context to the user prompt |
| `resolve_context` | `bool` | `True` | If True, resolve the context (i.e. call any functions in the context) before running the agent |
| `memory` | `Optional[Memory]` | `None` | Agent Memory |
| `add_history_to_messages` | `bool` | `False` | Add chat history to the messages sent to the Model |
| `num_history_runs` | `int` | `3` | Number of historical runs to include in the messages. |
| `search_previous_sessions_history` | `bool` | `False` | Set this to `True` to allow searching through previous sessions. |
| `num_history_sessions` | `int` | `2` | Specify the number of past sessions to include in the search. It's advisable to keep this number to 2 or 3 for now, as a larger number might fill up the context length of the model, potentially leading to performance issues. |
| `knowledge` | `Optional[AgentKnowledge]` | `None` | Agent Knowledge |
| `knowledge_filters` | `Optional[Dict[str, Any]]` | `None` | Knowledge filters to apply to the knowledge base |
| `enable_agentic_knowledge_filters` | `bool` | `False` | Enable agentic knowledge filters |
| `add_references` | `bool` | `False` | Enable RAG by adding references from AgentKnowledge to the user prompt |
| `retriever` | `Optional[Callable[..., Optional[List[Dict]]]]` | `None` | Function to get references to add to the user_message |
| `references_format` | `Literal["json", "yaml"]` | `"json"` | Format of the references |
| `storage` | `Optional[AgentStorage]` | `None` | Agent Storage |
| `extra_data` | `Optional[Dict[str, Any]]` | `None` | Extra data stored with this agent |
| `tools` | `Optional[List[Union[Toolkit, Callable, Function]]]` | `None` | A list of tools provided to the Model |
| `show_tool_calls` | `bool` | `False` | Show tool calls in Agent response |
| `tool_call_limit` | `Optional[int]` | `None` | Maximum number of tool calls allowed for a single run |
| `tool_choice` | `Optional[Union[str, Dict[str, Any]]]` | `None` | Controls which (if any) tool is called by the model |
| `reasoning` | `bool` | `False` | Enable reasoning by working through the problem step by step |
| `reasoning_model` | `Optional[Model]` | `None` | Model to use for reasoning |
| `reasoning_agent` | `Optional[Agent]` | `None` | Agent to use for reasoning |
| `reasoning_min_steps` | `int` | `1` | Minimum number of reasoning steps |
| `reasoning_max_steps` | `int` | `10` | Maximum number of reasoning steps |
| `read_chat_history` | `bool` | `False` | Add a tool that allows the Model to read the chat history |
| `search_knowledge` | `bool` | `True` | Add a tool that allows the Model to search the knowledge base |
| `update_knowledge` | `bool` | `False` | Add a tool that allows the Model to update the knowledge base |
| `read_tool_call_history` | `bool` | `False` | Add a tool that allows the Model to get the tool call history |
| `system_message` | `Optional[Union[str, Callable, Message]]` | `None` | Provide the system message as a string or function. This overrides `description`, `goal`, `instructions`, etc. and sends the provided system message as-is.|
| `system_message_role` | `str` | `"system"` | Role for the system message |
| `create_default_system_message` | `bool` | `True` | If True, create a default system message using agent settings |
| `description` | `Optional[str]` | `None` | A description of the Agent that is added to the start of the system message |
| `goal` | `Optional[str]` | `None` | The goal of this task |
| `success_criteria` | `Optional[str]` | `None` | Success criteria for the agent |
| `instructions` | `Optional[Union[str, List[str], Callable]]` | `None` | List of instructions for the agent |
| `expected_output` | `Optional[str]` | `None` | Provide the expected output from the Agent |
| `additional_context` | `Optional[str]` | `None` | Additional context added to the end of the system message |
| `markdown` | `bool` | `False` | If markdown=true, add instructions to format the output using markdown |
| `add_name_to_instructions` | `bool` | `False` | If True, add the agent name to the instructions |
| `add_datetime_to_instructions` | `bool` | `False` | If True, add the current datetime to the system message |
| `add_location_to_instructions` | `bool` | `False` | If True, add the current location to the system message |
| `add_state_in_messages` | `bool` | `False` | If True, add the session state variables in messages |
| `add_messages` | `Optional[List[Union[Dict, Message]]]` | `None` | A list of extra messages added after the system message |
| `user_message` | `Optional[Union[List, Dict, str, Callable, Message]]` | `None` | Provide the user message |
| `user_message_role` | `str` | `"user"` | Role for the user message |
| `create_default_user_message` | `bool` | `True` | If True, create a default user message |
| `retries` | `int` | `0` | Number of retries to attempt |
| `delay_between_retries` | `int` | `1` | Delay between retries |
| `exponential_backoff` | `bool` | `False` | If True, the delay between retries is doubled each time |
| `response_model` | `Optional[Type[BaseModel]]` | `None` | Provide a response model to get the response as a Pydantic model |
| `parse_response` | `bool` | `True` | If True, the response is converted into the response_model |
| `use_json_mode` | `bool` | `False` | If `response_model` is set, sets the response "mode" of the model, i.e. if the model should explicitly respond with a JSON object instead of a Pydantic model |
| `save_response_to_file` | `Optional[str]` | `None` | Save the response to a file |
| `stream` | `Optional[bool]` | `None` | Stream the response from the Agent |
| `stream_intermediate_steps` | `bool` | `False` | Stream the intermediate steps from the Agent |
| `store_events` | `bool` | `False` | Store the streaming events on the RunResponse |
| `events_to_skip` | `Optional[List[RunEvent]]` | `None` | Specify which event types to skip when storing events on the RunResponse |
| `team` | `Optional[List[Agent]]` | `None` | The team of agents that this agent can transfer tasks to |
| `team_data` | `Optional[Dict[str, Any]]` | `None` | Data shared between team members |
| `role` | `Optional[str]` | `None` | If this Agent is part of a team, this is the role of the agent |
| `respond_directly` | `bool` | `False` | If True, member agent responds directly to user |
| `add_transfer_instructions` | `bool` | `True` | Add instructions for transferring tasks to team members |
| `team_response_separator` | `str` | `"\n"` | Separator between responses from the team |
| `debug_mode` | `bool` | `False` | Enable debug logs |
| `monitoring` | `bool` | `False` | Log Agent information to agno.com for monitoring |
| `telemetry` | `bool` | `True` | Log minimal telemetry for analytics |

## Functions

### `print_response`

Run the agent and print the response.

**Parameters:**
- `message` (Optional[Union[List, Dict, str, Message]]): The message to send to the agent
- `session_id` (Optional[str]): Session ID to use
- `user_id` (Optional[str]): User ID to use
- `messages` (Optional[List[Union[Dict, Message]]]): List of additional messages to use
- `audio` (Optional[Sequence[Audio]]): Audio files to include
- `images` (Optional[Sequence[Image]]): Image files to include
- `videos` (Optional[Sequence[Video]]): Video files to include
- `files` (Optional[Sequence[File]]): Files to include
- `stream` (Optional[bool]): Whether to stream the response
- `stream_intermediate_steps` (bool): Whether to stream intermediate steps
- `markdown` (bool): Whether to format output as markdown
- `show_message` (bool): Whether to show the message
- `show_reasoning` (bool): Whether to show reasoning
- `show_full_reasoning` (bool): Whether to show full reasoning
- `console` (Optional[Any]): Console to use for output
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

### `run`

Run the agent.

**Parameters:**
- `message` (Optional[Union[str, List, Dict, Message]]): The message to send to the agent
- `stream` (Optional[bool]): Whether to stream the response
- `user_id` (Optional[str]): User ID to use
- `session_id` (Optional[str]): Session ID to use
- `audio` (Optional[Sequence[Audio]]): Audio files to include
- `images` (Optional[Sequence[Image]]): Image files to include
- `videos` (Optional[Sequence[Video]]): Video files to include
- `files` (Optional[Sequence[File]]): Files to include
- `messages` (Optional[Sequence[Union[Dict, Message]]]): List of additional messages to use
- `stream_intermediate_steps` (Optional[bool]): Whether to stream intermediate steps
- `retries` (Optional[int]): Number of retries to attempt
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

**Returns:**
- `Union[RunResponse, Iterator[RunResponseEvent]]`: Either a RunResponse or an iterator of RunResponseEvents, depending on the `stream` parameter

### `aprint_response`

Run the agent and print the response asynchronously.

**Parameters:**
- `message` (Optional[Union[List, Dict, str, Message]]): The message to send to the agent
- `session_id` (Optional[str]): Session ID to use
- `user_id` (Optional[str]): User ID to use
- `messages` (Optional[List[Union[Dict, Message]]]): List of additional messages to use
- `audio` (Optional[Sequence[Audio]]): Audio files to include
- `images` (Optional[Sequence[Image]]): Image files to include
- `videos` (Optional[Sequence[Video]]): Video files to include
- `files` (Optional[Sequence[File]]): Files to include
- `stream` (Optional[bool]): Whether to stream the response
- `stream_intermediate_steps` (bool): Whether to stream intermediate steps
- `markdown` (bool): Whether to format output as markdown
- `show_message` (bool): Whether to show the message
- `show_reasoning` (bool): Whether to show reasoning
- `show_full_reasoning` (bool): Whether to show full reasoning
- `console` (Optional[Any]): Console to use for output
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

### `arun`

Run the agent asynchronously.

**Parameters:**
- `message` (Optional[Union[str, List, Dict, Message]]): The message to send to the agent
- `stream` (Optional[bool]): Whether to stream the response
- `user_id` (Optional[str]): User ID to use
- `session_id` (Optional[str]): Session ID to use
- `audio` (Optional[Sequence[Audio]]): Audio files to include
- `images` (Optional[Sequence[Image]]): Image files to include
- `videos` (Optional[Sequence[Video]]): Video files to include
- `files` (Optional[Sequence[File]]): Files to include
- `messages` (Optional[Sequence[Union[Dict, Message]]]): List of additional messages to use
- `stream_intermediate_steps` (Optional[bool]): Whether to stream intermediate steps
- `retries` (Optional[int]): Number of retries to attempt
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

**Returns:**
- `Union[RunResponse, AsyncIterator[RunResponseEvent]]`: Either a RunResponse or an iterator of RunResponseEvents, depending on the `stream` parameter


### `continue_run`

Continue a run.

**Parameters:**
- `run_response` (Optional[RunResponse]): The run response to continue
- `run_id` (Optional[str]): The run ID to continue
- `updated_tools` (Optional[List[ToolExecution]]): Updated tools to use, required if the run is resumed using `run_id`
- `stream` (Optional[bool]): Whether to stream the response
- `stream_intermediate_steps` (Optional[bool]): Whether to stream intermediate steps
- `user_id` (Optional[str]): User ID to use
- `session_id` (Optional[str]): Session ID to use
- `retries` (Optional[int]): Number of retries to attempt
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

**Returns:**
- `Union[RunResponse, Iterator[RunResponseEvent]]`: Either a RunResponse or an iterator of RunResponseEvents, depending on the `stream` parameter

### `acontinue_run`

Continue a run asynchronously.

**Parameters:**
- `run_response` (Optional[RunResponse]): The run response to continue
- `run_id` (Optional[str]): The run ID to continue
- `updated_tools` (Optional[List[ToolExecution]]): Updated tools to use, required if the run is resumed using `run_id`
- `stream` (Optional[bool]): Whether to stream the response
- `stream_intermediate_steps` (Optional[bool]): Whether to stream intermediate steps
- `user_id` (Optional[str]): User ID to use
- `session_id` (Optional[str]): Session ID to use
- `retries` (Optional[int]): Number of retries to attempt
- `knowledge_filters` (Optional[Dict[str, Any]]): Knowledge filters to apply

**Returns:**
- `Union[RunResponse, AsyncIterator[RunResponseEvent]]`: Either a RunResponse or an iterator of RunResponseEvents, depending on the `stream` parameter

### get_session_summary

Get the session summary for the given session ID and user ID.

**Parameters:**
- `session_id` (Optional[str]): Session ID to use (if not provided, the current session is used)
- `user_id` (Optional[str]): User ID to use (if not provided, the current user is used)

**Returns:**
- `Optional[SessionSummary]`: The session summary

### get_user_memories

Get the user memories for the given user ID.

**Parameters:**
- `user_id` (Optional[str]): User ID to use (if not provided, the current user is used)

**Returns:**
- `Optional[List[UserMemory]]`: The user memories



