| Parameter  | Type            | Default                                    | Description                                                                                                              |
| ---------- | --------------- | ------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------ |
| `id`       | `str`           | `"sonar-pro"`            | The specific model ID used for generating responses.                                                                     |
| `name`     | `str`           | `"Perplexity"`                                 | The name identifier for the Perplexity agent.                                                                                |
| `provider` | `str`           | `"Perplexity" + id`                                           | The provider of the model, combining "Perplexity" with the model ID.                                                         |
| `api_key`  | `Optional[str]` | -                                          | The API key for authenticating requests to the Perplexity service. Retrieved from the environment variable `PERPLEXITY_API_KEY`. |
| `base_url` | `str`           | `"https://api.perplexity.ai/"`    | The base URL for making API requests to the Perplexity service.                                                              |
| `max_tokens` | `int`           | `1024`    | The maximum number of tokens to generate in the response.                                                                     |
