| Parameter           | Type                                                      | Default                  | Description                                                                                                                                                                                  |
| ------------------- | --------------------------------------------------------- | ------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                | `str`                                                     | `"mistral-large-latest"` | The ID of the model.                                                                                                                                                                         |
| `name`              | `str`                                                     | `"MistralChat"`          | The name of the model.                                                                                                                                                                       |
| `provider`          | `str`                                                     | `"Mistral"`              | The provider of the model.                                                                                                                                                                   |
| `temperature`       | `Optional[float]`                                         | `None`                   | Controls randomness in output generation.                                                                                                                                                    |
| `max_tokens`        | `Optional[int]`                                           | `None`                   | Maximum number of tokens to generate.                                                                                                                                                        |
| `top_p`             | `Optional[float]`                                         | `None`                   | Controls diversity of output generation.                                                                                                                                                     |
| `random_seed`       | `Optional[int]`                                           | `None`                   | Seed for random number generation.                                                                                                                                                           |
| `safe_mode`         | `bool`                                                    | `False`                  | Enables content filtering.                                                                                                                                                                   |
| `safe_prompt`       | `bool`                                                    | `False`                  | Applies content filtering to prompts.                                                                                                                                                        |
| `response_format`   | `Optional[Union[Dict[str, Any], ChatCompletionResponse]]` | `None`                   | Specifies the desired response format.                                                                                                                                                       |
| `request_params`    | `Optional[Dict[str, Any]]`                                | `None`                   | Additional request parameters.                                                                                                                                                               |
| `api_key`           | `Optional[str]`                                           | `None`                   | Your Mistral API key.                                                                                                                                                                        |
| `endpoint`          | `Optional[str]`                                           | `None`                   | Custom API endpoint URL.                                                                                                                                                                     |
| `max_retries`       | `Optional[int]`                                           | `None`                   | Maximum number of API call retries.                                                                                                                                                          |
| `timeout`           | `Optional[int]`                                           | `None`                   | Timeout for API calls in seconds.                                                                                                                                                            |
| `client_params`     | `Optional[Dict[str, Any]]`                                | `None`                   | Additional client parameters.                                                                                                                                                                |
| `mistral_client`    | `Optional[MistralClient]`                                 | `None`                   | Custom Mistral client instance.                                                                                                                                                              |
| `store`             | `Optional[bool]`                                          | `None`                   | Whether or not to store the output of this chat completion request for use in the model distillation or evals products.                                                                      |
| `frequency_penalty` | `Optional[float]`                                         | `None`                   | A number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model's likelihood to repeat the same line verbatim. |
| `logit_bias`        | `Optional[Any]`                                           | `None`                   | A JSON object that modifies the likelihood of specified tokens appearing in the completion by mapping token IDs to bias values between -100 and 100.                                         |
| `logprobs`          | `Optional[bool]`                                          | `None`                   | Whether to return log probabilities of the output tokens.                                                                                                                                    |
| `presence_penalty`  | `Optional[float]`                                         | `None`                   | A number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.              |
| `stop`              | `Optional[Union[str, List[str]]]`                         | `None`                   | Up to 4 sequences where the API will stop generating further tokens.                                                                                                                         |
| `top_logprobs`      | `Optional[int]`                                           | `None`                   | The number of top log probabilities to return for each generated token.                                                                                                                      |
| `user`              | `Optional[str]`                                           | `None`                   | A unique identifier representing your end-user, helping to monitor and detect abuse.                                                                                                         |
