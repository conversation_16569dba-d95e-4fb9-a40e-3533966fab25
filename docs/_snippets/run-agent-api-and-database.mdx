## Serve your Agents using FastAPI

[FastAPI](https://fastapi.tiangolo.com/) is an exceptional framework for building REST APIs. Its fast, well-designed and loved by everyone using it. Most production applications are built using a front-end framework like [next.js](https://nextjs.org) backed by a REST API, where FastAPI shines.

Your codebase comes pre-configured with [FastAPI](https://fastapi.tiangolo.com/) and [PostgreSQL](https://www.postgresql.org/), along with some sample routes. Start your workspace using:

<CodeGroup>

```bash terminal
ag ws up
```

```bash shorthand
ag ws up dev:docker
```

</CodeGroup>

**Press Enter** to confirm and give a few minutes for the image to download (only the first time). Verify container status and view logs on the docker dashboard.

- Open [localhost:8000/docs](http://localhost:8000/docs) to view the API Endpoints.
- Test the `/v1/agents/{agent_id}/run` endpoint with

```json
{
  "message": "howdy",
  "agent_id": "sage",
  "stream": true
}
```
