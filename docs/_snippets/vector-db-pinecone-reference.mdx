| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `name` | `str` | Required | Name of the Pinecone index |
| `dimension` | `int` | Required | Dimension of the embeddings |
| `spec` | `Union[Dict, ServerlessSpec, PodSpec]` | Required | Index specification for Pinecone |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `metric` | `Optional[str]` | `"cosine"` | Distance metric for similarity search |
| `additional_headers` | `Optional[Dict[str, str]]` | `{}` | Additional headers for Pinecone client |
| `pool_threads` | `Optional[int]` | `1` | Number of threads for Pinecone client |
| `namespace` | `Optional[str]` | `None` | Namespace for document storage |
| `timeout` | `Optional[int]` | `None` | Timeout for Pinecone operations |
| `index_api` | `Optional[Any]` | `None` | Custom Index API object |
| `api_key` | `Optional[str]` | `None` | Pinecone API key |
| `host` | `Optional[str]` | `None` | Pinecone host URL |
| `config` | `Optional[Config]` | `None` | Pinecone configuration object |
| `use_hybrid_search` | `bool` | `False` | Enable hybrid search (vector + keyword) |
| `hybrid_alpha` | `float` | `0.5` | Weight between vector and keyword search |
| `reranker` | `Optional[Reranker]` | `None` | Reranker for post-processing results |
