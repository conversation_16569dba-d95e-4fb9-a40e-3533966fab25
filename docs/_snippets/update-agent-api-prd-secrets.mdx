## Update Secrets

<Steps>
  <Step title="RDS database password">
    Update the RDS database password in `workspace/secrets/prd_db_secrets.yml`

    ```python workspace/secrets/prd_db_secrets.yml
    # Secrets used by prd RDS database
    MASTER_USERNAME: api
    MASTER_USER_PASSWORD: "api9999!!"
    ```

  </Step>
  <Step title="API Secrets">
    Add any other secrets used by your api to `workspace/secrets/prd_api_secrets.yml`

    ```python workspace/secrets/prd_api_secrets.yml
    SECRET_KEY: "very_secret"
    # OPENAI_API_KEY: "sk-***"
    ```

  </Step>
</Steps>
