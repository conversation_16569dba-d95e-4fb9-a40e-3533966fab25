| Parameter                | Type                                   | Description                                                        | Default |
|--------------------------|----------------------------------------|--------------------------------------------------------------------|---------|
| `max_completion_tokens`  | `Optional[int]`                        | Maximum tokens in completion                                       | None    |
| `repetition_penalty`     | `Optional[float]`                      | Penalty for token repetition                                       | None    |
| `temperature`            | `Optional[float]`                      | Sampling temperature                                               | None    |
| `top_p`                  | `Optional[float]`                      | Nucleus sampling parameter                                         | None    |
| `top_k`                  | `Optional[int]`                        | Top-k sampling parameter                                           | None    |
| `extra_headers`          | `Optional[Any]`                        | Additional HTTP headers to include in the API request              | None    |
| `extra_query`            | `Optional[Any]`                        | Additional query parameters to include in the API request          | None    |
| `extra_body`             | `Optional[Any]`                        | Additional body parameters to include in the API request           | None    |
| `request_params`         | `Optional[Dict[str, Any]>`             | Custom request parameters dictionary, merged into the API request  | None    |
| `api_key`                | `Optional[str]`                        | Llama API key (overrides `LLAMA_API_KEY` environment variable)      | None    |
| `base_url`               | `Optional[Union[str, httpx.URL]]`      | Base URL for the Llama API                                          | None    |
| `timeout`                | `Optional[float]`                      | Timeout for API requests (seconds)                                 | None    |
| `max_retries`            | `Optional[int]`                        | Maximum number of retries for API calls                            | None    |
| `default_headers`        | `Optional[Any]`                        | Default HTTP headers for the API client                            | None    |
| `default_query`          | `Optional[Any]`                        | Default query parameters for the API client                        | None    |
| `http_client`            | `Optional[httpx.Client]`               | Custom synchronous HTTP client instance                            | None    |
| `client_params`          | `Optional[Dict[str, Any]>`             | Additional parameters for the HTTP client constructor              | None    |
