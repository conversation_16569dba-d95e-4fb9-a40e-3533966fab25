### Core Features

| Agno Supported Models | Tool Support | Response Models | Knowledge | History / Storage | Async Execution | Async Tool Support |
| --------------------- | :----------: | :-------------: | :-------: | :---------------: | :-------------: | :----------------: |
| Anthropic Claude      |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| AWS Bedrock           |      ✅      |       ✅        |    ✅     |        ✅         |                 |                    |
| AWS Bedrock Claude    |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Azure AI Foundry      |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Azure OpenAI          |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Cohere                |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| DeepInfra             |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| DeepSeek              |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Fireworks             |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Gemini                |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Groq                  |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| HuggingFace           |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| IBM WatsonX           |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| InternLM              |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| LiteLLM               |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| LiteLLMOpenAI         |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| LM Studio             |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Mistral               |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Nvidia                |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Ollama                |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| OllamaTools           |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| OpenAIChat            |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| OpenAIResponses       |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| OpenRouter            |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Perplexity            |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Sambanova             |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| Together              |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |
| XAI                   |      ✅      |       ✅        |    ✅     |        ✅         |       ✅        |         ✅         |

<Note>

HuggingFace supports tool calling through the Agno framework, but not for streaming
responses.

</Note>

<Note>

Perplexity supports tool calling through the Agno framework, but their models don't
natively support tool calls in a straightforward way. This means tool usage may
be less reliable compared to other providers.

</Note>

### Multimodal Support

| Agno Supported Models | Image Input | Audio Input | Audio Responses | Video Input | File Upload |
| --------------------- | :---------: | :---------: | :-------------: | :---------: | :---------: |
| Anthropic Claude      |     ✅      |             |                 |             |     ✅      |
| AWS Bedrock           |     ✅      |             |                 |             |             |
| AWS Bedrock Claude    |     ✅      |             |                 |             |             |
| Azure AI Foundry      |     ✅      |             |                 |             |             |
| Azure OpenAI          |     ✅      |             |                 |             |             |
| Cohere                |             |             |                 |             |             |
| AWS Bedrock           |     ✅      |             |                 |             |             |
| AWS Bedrock Claude    |     ✅      |             |                 |             |             |
| Azure AI Foundry      |     ✅      |             |                 |             |             |
| Azure OpenAI          |     ✅      |             |                 |             |             |
| Cohere                |     ✅      |             |                 |             |             |
| DeepInfra             |             |             |                 |             |             |
| DeepSeek              |             |             |                 |             |             |
| Fireworks             |             |             |                 |             |             |
| Gemini                |     ✅      |     ✅      |                 |     ✅      |     ✅      |
| Groq                  |     ✅      |             |                 |             |             |
| HuggingFace           |     ✅      |             |                 |             |             |
| IBM WatsonX           |     ✅      |             |                 |             |             |
| InternLM              |             |             |                 |             |             |
| LiteLLM               |             |             |                 |             |             |
| LiteLLMOpenAI         |             |             |                 |             |             |
| LM Studio             |     ✅      |             |                 |             |             |
| Mistral               |     ✅      |             |                 |             |             |
| Nvidia                |             |             |                 |             |             |
| Ollama                |     ✅      |             |                 |             |             |
| OllamaTools           |             |             |                 |             |             |
| OpenAIChat            |     ✅      |     ✅      |       ✅        |             |             |
| OpenAIResponses       |     ✅      |     ✅      |       ✅        |             |     ✅      |
| OpenRouter            |             |             |                 |             |             |
| Perplexity            |             |             |                 |             |             |
| Sambanova             |             |             |                 |             |             |
| Together              |     ✅      |             |                 |             |             |
| XAI                   |     ✅      |             |                 |             |             |

### Structured Outputs

| Agno Supported Models | Structured Outputs | JSON Mode |
| --------------------- | :----------------: | :-------: |
| Anthropic Claude      |                    |    ✅     |
| AWS Bedrock           |                    |    ✅     |
| AWS Bedrock Claude    |                    |    ✅     |
| Azure AI Foundry      |                    |    ✅     |
| Azure OpenAI          |         ✅         |    ✅     |
| Cohere                |                    |    ✅     |
| DeepInfra             |                    |    ✅     |
| DeepSeek              |                    |    ✅     |
| Fireworks             |         ✅         |    ✅     |
| Gemini                |         ✅         |    ✅     |
| Groq                  |                    |    ✅     |
| HuggingFace           |                    |    ✅     |
| IBM WatsonX           |                    |    ✅     |
| InternLM              |         ✅         |    ✅     |
| LiteLLMOpenAI         |         ✅         |    ✅     |
| LiteLLM               |                    |    ✅     |
| LM Studio             |         ✅         |    ✅     |
| Mistral               |         ✅         |    ✅     |
| Nvidia                |                    |    ✅     |
| Ollama                |         ✅         |    ✅     |
| OllamaTools           |         ✅         |    ✅     |
| OpenAIChat            |         ✅         |    ✅     |
| OpenAIResponses       |         ✅         |    ✅     |
| OpenRouter            |         ✅         |    ✅     |
| Perplexity            |         ✅         |    ✅     |
| Sambanova             |                    |    ✅     |
| Together              |         ✅         |    ✅     |
| XAI                   |         ✅         |    ✅     |

<Note>

LM Studio supports JSON schema output, but not structured outputs.

</Note>

Read more about [Structured Outputs](/faq/structured-outputs).
