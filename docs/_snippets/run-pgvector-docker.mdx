## Run PgVector on Docker

Create a file `resources.py` with the following contents:

```python resources.py
from agno.docker.app.postgres import PgVectorDb
from agno.docker.resources import DockerResources

# -*- PgVector running on port 5432:5432
vector_db = PgVectorDb(
    pg_user="ai",
    pg_password="ai",
    pg_database="ai",
    debug_mode=True,
)

# -*- DockerResources
dev_docker_resources = DockerResources(apps=[vector_db])
```

Start resources using:

<CodeGroup>

```bash Mac
ag start resources.py
```

```bash Windows
ag start resources.py
```

</CodeGroup>

**Press Enter** to confirm and verify container status on the docker dashboard.
