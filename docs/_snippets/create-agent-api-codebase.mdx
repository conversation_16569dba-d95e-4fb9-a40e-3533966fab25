## Create your Agent API codebase

Create your codebase using the `agent-api` template, give it any name you like.

<CodeGroup>

```bash Mac
ag ws create --template agent-api --name agent-api
```

```bash Windows
ag ws create --template agent-api --name agent-api
```

</CodeGroup>

This will create a folder `agent-api` with the following structure:

```bash
agent-api                     # root directory
├── agents                  # add your Agents here
├── api                     # add fastApi routes here
├── db                      # add database tables here
├── Dockerfile              # Dockerfile for the application
├── pyproject.toml          # python project definition
├── requirements.txt        # python dependencies generated by pyproject.toml
├── scripts                 # helper scripts
├── utils                   # shared utilities
└── workspace               # agno workspace directory
    ├── dev_resources.py    # dev resources running locally
    ├── prd_resources.py    # production resources running on AWS
    ├── secrets             # secrets
    └── settings.py         # agno workspace settings
```
