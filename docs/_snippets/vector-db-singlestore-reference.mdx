| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `collection` | `str` | Required | Name of the SingleStore table |
| `schema` | `Optional[str]` | `"ai"` | Schema name for the table |
| `db_url` | `Optional[str]` | `None` | Database connection URL |
| `db_engine` | `Optional[Engine]` | `None` | SQLAlchemy engine instance |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `distance` | `Distance` | `Distance.cosine` | Distance metric for similarity search |
| `reranker` | `Optional[Reranker]` | `None` | Reranker for post-processing results |
