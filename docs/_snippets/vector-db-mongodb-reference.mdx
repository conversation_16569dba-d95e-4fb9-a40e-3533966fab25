| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `collection_name` | `str` | Required | Name of the MongoDB collection to store vectors and metadata |
| `db_url` | `Optional[str]` | `"mongodb://localhost:27017/"` | MongoDB connection string |
| `database` | `str` | `"ai"` | Name of the MongoDB database |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `distance_metric` | `str` | `Distance.cosine` | Distance metric to use for similarity search |
| `overwrite` | `bool` | `False` | Whether to overwrite existing collection and index |
| `cosmos_compatibility` | `bool` | `False` | Whether to enable support for azure cosmos db mongodb vcore |
| `wait_until_index_ready_in_seconds` | `Optional[float]` | `None` | Time in seconds to wait until the index is ready |
| `wait_after_insert_in_seconds` | `Optional[float]` | `None` | Time in seconds to wait after inserting documents |
