| Parameter                    | Type            | Default | Description                                      |
| ---------------------------- | --------------- | ------- | ------------------------------------------------ |
| `table_name`                 | `str`           | -       | Name of the table to be used.                    |
| `region_name`                | `Optional[str]` | `None`  | Region name of the DynamoDB table.               |
| `aws_access_key_id`          | `Optional[str]` | `None`  | AWS access key id, if provided.                  |
| `aws_secret_access_key`      | `Optional[str]` | `None`  | AWS secret access key, if provided.              |
| `endpoint_url`               | `Optional[str]` | `None`  | Endpoint URL, if provided.                       |
| `create_table_if_not_exists` | `bool`          | `True`  | If true, creates the table if it does not exist. |
