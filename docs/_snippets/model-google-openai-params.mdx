| Parameter  | Type            | Default                                                | Description                                                                                |
| ---------- | --------------- | ------------------------------------------------------ | ------------------------------------------------------------------------------------------ |
| `id`       | `str`           | `"gemini-1.5-flash"`                                   | The ID of the Gemini model to use                                                          |
| `name`     | `str`           | `"Gemini"`                                             | The name of this chat model instance                                                       |
| `provider` | `str`           | `"Google"`                                             | The provider of the model                                                                  |
| `api_key`  | `Optional[str]` | `None`                                                 | The API key for authenticating with Google (defaults to environment variable GOOGLE_API_KEY) |
| `base_url` | `str`           | `"https://generativelanguage.googleapis.com/v1beta/"` | The base URL for API requests                                                              |
