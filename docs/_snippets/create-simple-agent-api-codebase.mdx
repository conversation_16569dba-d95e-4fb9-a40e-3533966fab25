## Folder structure

The `agent-api` folder contains the following structure:

```bash
agent-api                     # root directory
├── agents                  # add your Agents here
├── api                     # add fastApi routes here
├── db                      # add database tables here
├── Dockerfile              # Dockerfile for the application
├── pyproject.toml          # python project definition
├── requirements.txt        # python dependencies generated by pyproject.toml
├── scripts                 # helper scripts
```

#### Prebuilt Agents

The `/agents` folder contains pre-built agents that you can use as a starting point.

- **Web Search Agent**: A simple agent that can search the web.
- **Agno Assist**: An Agent that can help answer questions about Agno.
    - **Important**: Make sure to load the `agno_assist` knowledge base before using this agent.
- **Finance Agent**: An agent that uses the Yahoo Finance API to get stock prices and financial data.