| Parameter | Type | Default | Description |
| --------- | ---- | ------- | ----------- |
| `collection` | `str` | Required | Name of the Qdrant collection |
| `embedder` | `Optional[Embedder]` | `OpenAIEmbedder()` | Embedder instance to generate embeddings |
| `distance` | `Distance` | `Distance.cosine` | Distance metric for vector comparisons |
| `location` | `Optional[str]` | `None` | Local storage path for Qdrant |
| `url` | `Optional[str]` | `None` | URL of the Qdrant server |
| `port` | `Optional[int]` | `6333` | HTTP port for Qdrant server |
| `grpc_port` | `int` | `6334` | gRPC port for Qdrant server |
| `prefer_grpc` | `bool` | `False` | Use gRPC instead of HTTP |
| `https` | `Optional[bool]` | `None` | Enable HTTPS connection |
| `api_key` | `Optional[str]` | `None` | API key for authentication |
| `prefix` | `Optional[str]` | `None` | URL prefix for Qdrant server |
| `timeout` | `Optional[float]` | `None` | Request timeout in seconds |
| `host` | `Optional[str]` | `None` | Host address of Qdrant server |
| `path` | `Optional[str]` | `None` | Path to local storage |
| `reranker` | `Optional[Reranker]` | `None` | Reranker for post-processing results |
