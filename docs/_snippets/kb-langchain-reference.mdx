| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `loader` | `Optional[Callable]` | `None` | Optional callable to load documents into the knowledge base |
| `vectorstore` | `Optional[Any]` | `None` | Optional vector store for document storage and retrieval |
| `search_kwargs` | `Optional[dict]` | `None` | Optional search parameters for the vector store |
| `retriever` | `Optional[Any]` | `None` | Optional retriever for fetching relevant documents |
