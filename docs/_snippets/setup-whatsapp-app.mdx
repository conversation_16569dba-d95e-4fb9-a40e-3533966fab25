## Setup and Configuration
<Steps>

<Step title="Prerequisites">
Ensure you have the following:
- A Meta Developer Account
- A Meta Business Account
- A valid Facebook account
- ngrok (for development)
- Python 3.7+
</Step>

<Step title="Create a Meta App">
1. Go to [Meta for Developers](https://developers.facebook.com/) and verify your account.
2. Create a new app at [Meta Apps Dashboard](https://developers.facebook.com/apps/).
3. Under "Use Case", select "Other".
4. Choose "Business" as the app type.
5. Provide:
    - App name
    - Contact email
6. Click "Create App".
</Step>

<Step title="Set Up a Meta Business Account">
1. Navigate to [Meta Business Manager](https://business.facebook.com/).
2. Create a new business account or use an existing one.
3. Verify your business by clicking on the email link.
4. Go to your App page, navigate to "App settings / Basic", and click "Start Verification" under "Business Verification". Complete the verification process for production.
5. Associate the app with your business account and click "Create App".
</Step>

<Step title="Setup WhatsApp Business API">
1. Go to your app's WhatsApp Setup page.
2. Click on "Start using the API" (API Setup).
3. Generate an Access Token.
4. Copy your Phone Number ID.
5. Copy your WhatsApp Business Account ID.
6. Add a "To" number that you will use for testing (this will likely be your personal number).
</Step>

<Step title="Setup Environment Variables">
Create a `.envrc` file in your project root with the following content, replacing placeholder values with your actual credentials:
```bash
export WHATSAPP_ACCESS_TOKEN="your_whatsapp_access_token"
export WHATSAPP_PHONE_NUMBER_ID="your_phone_number_id"
export WHATSAPP_WEBHOOK_URL="your_ngrok_url_plus_webhook_path" # e.g., https://xxxxx.ngrok-free.app/webhook
export WHATSAPP_VERIFY_TOKEN="your_chosen_verify_token" # A string you create
```
Ensure this file is sourced by your shell (e.g., by using `direnv allow`).
</Step>

<Step title="Setup Webhook with ngrok">
1. For local development, use ngrok to expose your local server to the internet. If you don't have a static ngrok URL, you'll need to update the `WHATSAPP_WEBHOOK_URL` environment variable and your Meta App webhook configuration each time ngrok assigns a new URL.
2. Run ngrok, ensuring the port matches the port your Agno WhatsApp app will run on (e.g., 8000):
   ```bash
   ngrok http 8000
   # Or, if you have a paid ngrok plan with a static domain:
   # ngrok http --domain=your-custom-domain.ngrok-free.app 8000
   ```
3.  Copy the `https://` URL provided by ngrok. This is your base ngrok URL.
4.  Construct your full webhook URL by appending `/webhook` (or your chosen prefix) to the ngrok URL (e.g., `https://<random-string>.ngrok-free.app/webhook`). Update `WHATSAPP_WEBHOOK_URL` in your `.envrc` if necessary.
5.  In your Meta App's WhatsApp Setup page, navigate to the "Webhook" section and click "Edit".
6.  Configure the webhook:
    -   **Callback URL**: Enter your full ngrok webhook URL.
    -   **Verify Token**: Enter the same value you used for `WHATSAPP_VERIFY_TOKEN` in your `.envrc` file.
7.  Click "Verify and save". Your Agno application must be running locally for verification to succeed.
8.  After successful verification, click "Manage" next to Webhook fields. Subscribe to the `messages` field under `whatsapp_business_account`.
</Step>

<Step title="Configure Application Environment">
Set the `APP_ENV` environment variable:
- For **Development Mode**:
  ```bash
  export APP_ENV="development"
  ```
  (Webhook signature validation might be less strict or bypassed).
- For **Production Mode**:
  ```bash
  export APP_ENV="production"
  ```
  You will also need to set the `WHATSAPP_APP_SECRET` for webhook signature validation:
  ```bash
  export WHATSAPP_APP_SECRET="your_meta_app_secret"
  ```
  This should be the "App Secret" found in your Meta App's "App settings > Basic" page.
</Step>

</Steps>