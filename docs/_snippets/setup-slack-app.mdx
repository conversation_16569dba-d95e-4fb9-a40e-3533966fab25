## Setup and Configuration
<Steps>

<Step title="Prerequisites">
Ensure you have the following:
- A Slack workspace with admin privileges
- ngrok (for development)
- Python 3.7+
</Step>

<Step title="Create a Slack App">
1. Go to [Slack App Directory](https://api.slack.com/apps)
2. Click "Create New App"
3. Select "From scratch"
4. Provide:
    - App name
    - Workspace to install to
5. Click "Create App"
</Step>

<Step title="Configure OAuth & Permissions">
1. Navigate to "OAuth & Permissions" in your Slack App settings
2. Under "Scopes", click "Add an OAuth Scope"
3. Add the following Bot Token Scopes:
   - `app_mention`
   - `chat:write`
   - `chat:write.customize`
   - `chat:write.public`
   - `im:history`
   - `im:read`
   - `im:write`
4. Scroll to the top and click "Install to Workspace"
5. Click "Allow" to authorize the app
</Step>

<Step title="Setup Environment Variables">
Create a `.envrc` file in your project root with the following content, replacing placeholder values with your actual credentials:
```bash
export SLACK_TOKEN="xoxb-your-bot-user-token"  # Bot User OAuth Token
export SLACK_SIGNING_SECRET="your-signing-secret"  # App Signing Secret
```

Find these values in your Slack App settings:
- Bot User OAuth Token: Under "OAuth & Permissions"
- Signing Secret: Under "Basic Information" > "App Credentials"

Ensure this file is sourced by your shell (e.g., by using `direnv allow`).
</Step>

<Step title="Setup Webhook with ngrok">
1. For local development, use ngrok to expose your local server to the internet:
   ```bash
   ngrok http 8000
   # Or, if you have a paid ngrok plan with a static domain:
   # ngrok http --domain=your-custom-domain.ngrok-free.app 8000
   ```
2. Copy the `https://` URL provided by ngrok
3. In your Slack App settings, go to "Event Subscriptions"
4. Enable events by toggling the switch
5. Add your Request URL:
   - Format: `https://your-ngrok-url.ngrok.io/slack/events`
6. Wait for Slack to verify the endpoint (your app must be running)
</Step>

<Step title="Configure Event Subscriptions">
1. Under "Subscribe to bot events" in Event Subscriptions:
2. Click "Add Bot User Event" and add:
   - `app_mention`
   - `message.im`
   - `message.channels`
   - `message.groups`
3. Click "Save Changes"
4. Reinstall your app to apply the new permissions
</Step>

<Step title="Enable App Home">
1. Go to "App Home" in your Slack App settings
2. Under "Show Tabs":
   - Enable "Messages Tab"
   - Check "Allow users to send Slash commands and messages from the messages tab"
3. Save changes
</Step>

<Step title="Final Installation">
1. Go back to "Install App" in your Slack App settings
2. Click "Reinstall to Workspace"
3. Authorize the app with the new permissions
4. Your app is now ready to use!
</Step>

</Steps>
