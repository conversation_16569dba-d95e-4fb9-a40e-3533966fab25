### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `id` | `str` | The model ID to use for embeddings | `"embed-english-v3.0"` |
| `input_type` | `str` | Type of input being embedded (e.g., "search_query", "classification", "clustering") | `"search_query"` |
| `embedding_types` | `Optional[List[str]]` | List of embedding types to generate | `None` |
| `api_key` | `Optional[str]` | Cohere API key | Environment variable `COHERE_API_KEY` |
| `request_params` | `Optional[Dict[str, Any]]` | Additional parameters for embedding requests | `None` |
| `client_params` | `Optional[Dict[str, Any]]` | Additional parameters for client initialization | `None` |
| `cohere_client` | `Optional[CohereClient]` | Pre-configured Cohere client | `None` |