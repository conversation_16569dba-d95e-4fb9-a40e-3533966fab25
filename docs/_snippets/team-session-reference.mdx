| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `session_id` | `str` | Required | Session UUID |
| `team_id` | `Optional[str]` | `None` | ID of the team that this session is associated with |
| `user_id` | `Optional[str]` | `None` | ID of the user interacting with this team |
| `memory` | `Optional[Dict[str, Any]]` | `None` | Team Memory |
| `team_data` | `Optional[Dict[str, Any]]` | `None` | Team Data: team_id, name and model |
| `session_data` | `Optional[Dict[str, Any]]` | `None` | Session Data: session_name, session_state, images, videos, audio |
| `extra_data` | `Optional[Dict[str, Any]]` | `None` | Extra Data stored with this team |
| `created_at` | `Optional[int]` | `None` | The unix timestamp when this session was created |
| `updated_at` | `Optional[int]` | `None` | The unix timestamp when this session was last updated |
