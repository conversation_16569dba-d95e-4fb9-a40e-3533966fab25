## Running in Production

This repository includes a `Dockerfile` for building a production-ready container image of the application.

The general process to run in production is:

1.  Update the `scripts/build_image.sh` file and set your `IMAGE_NAME` and `IMAGE_TAG` variables.
2.  Build and push the image to your container registry:
    ```bash
    ./scripts/build_image.sh
    ```
3.  Run in your cloud provider of choice.

### Detailed Steps

#### 1. Configure for Production

- Ensure your production environment variables (e.g., `OPENAI_API_KEY`, database connection strings) are securely managed. Most cloud providers offer a way to set these as environment variables for your deployed service.
- Review the agent configurations in the `/agents` directory and ensure they are set up for your production needs (e.g., correct model versions, any production-specific settings).

#### 2. Build Your Production Docker Image

Update the `scripts/build_image.sh` script to set your desired `IMAGE_NAME` and `IMAGE_TAG` (e.g., `your-repo/agent-api:v1.0.0`).

Run the script to build and push the image:

```bash
./scripts/build_image.sh
```

#### 3. Deploy to a Cloud Service

With your image in a registry, you can deploy it to various cloud services that support containerized applications. Some common options include:

**Serverless Container Platforms:**

-   [Google Cloud Run](https://cloud.google.com/run): A fully managed platform that automatically scales your stateless containers.
-   [AWS App Runner](https://aws.amazon.com/apprunner/): Makes it easy to deploy containerized web applications and APIs at scale.
-   [Azure Container Apps](https://azure.microsoft.com/en-us/products/container-apps): Build and deploy modern apps and microservices using serverless containers.

**Container Orchestration Services:**

-   [Amazon Elastic Container Service (ECS)](https://aws.amazon.com/ecs/): Often used with AWS Fargate for serverless compute or EC2 instances.
-   [Google Kubernetes Engine (GKE)](https://cloud.google.com/kubernetes-engine): Managed Kubernetes service.
-   [Azure Kubernetes Service (AKS)](https://azure.microsoft.com/en-us/services/kubernetes-service/): Managed Kubernetes service.

**Platform as a Service (PaaS) with Docker Support:**

-   [Railway.app](https://railway.app/): Simple deployment from a Dockerfile.
-   [Render](https://render.com/): Simplifies deploying Docker containers, databases, and static sites.
-   [Heroku](https://www.heroku.com/): Supports deploying Docker containers.

**Specialized Platforms:**

-   [Modal](https://modal.com/): Platform for running Python code in the cloud, can serve web endpoints.

The specific deployment steps will vary depending on the chosen provider. Generally, you'll point the service to your container image in the registry and configure port mapping (application runs on port `8000` by default), environment variables, scaling, and database connections.

#### 4. Database Configuration

The default `docker-compose.yml` sets up a PostgreSQL database for local development. In production, use a managed database service (e.g., AWS RDS, Google Cloud SQL, Azure Database for PostgreSQL).
Ensure your deployed application is configured with the correct database connection URL for your production database, usually via environment variables.
