---
title: WhatsA<PERSON> Tools
---

## Code

```python cookbook/tools/whatsapp_tools.py
from agno.agent import Agent
from agno.models.google import Gemini
from agno.tools.whatsapp import WhatsAppTools

agent = Agent(
    name="whatsapp",
    model=Gemini(id="gemini-2.0-flash"),
    tools=[WhatsAppTools()],
)

# Example: Send a template message
# Note: Replace '''hello_world''' with your actual template name
agent.print_response(
    "Send a template message using the '''hello_world''' template in English to +91 1234567890"
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set up WhatsApp Business API">
    1. Go to [Meta for Developers](https://developers.facebook.com/docs/whatsapp/cloud-api/get-started)
    2. Create a Meta App and set up the WhatsApp Business API.
    3. Obtain your Phone Number ID and a permanent System User Access Token.
  </Step>

  <Step title="Set your API keys and identifiers">
    ```bash
    export WHATSAPP_ACCESS_TOKEN=xxx
    export WHATSAPP_PHONE_NUMBER_ID=xxx
    export OPENAI_API_KEY=xxx # Or your preferred LLM API key
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U agno openai google-generativeai # Add any other necessary WhatsApp SDKs
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/whatsapp_tools.py
    ```

    ```bash Windows
    python cookbook/tools/whatsapp_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>
