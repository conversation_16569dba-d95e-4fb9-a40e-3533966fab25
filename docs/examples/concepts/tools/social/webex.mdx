---
title: Webex Tools
---

## Code

```python cookbook/tools/webex_tools.py
from agno.agent import Agent
from agno.tools.webex import WebexTools

agent = Agent(
    name="Webex Assistant",
    tools=[WebexTools()],
    description="You are a Webex assistant that can send messages and manage spaces.",
    instructions=[
        "You can help users by:",
        "- Listing available Webex spaces",
        "- Sending messages to spaces",
        "Always confirm the space exists before sending messages.",
    ],
    show_tool_calls=True,
    markdown=True,
)

# List all spaces in Webex
agent.print_response("List all spaces on our Webex", markdown=True)

# Send a message to a Space in Webex
agent.print_response(
    "Send a funny ice-breaking message to the webex Welcome space", markdown=True
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set up Webex Bot">
    1. Go to [Webex Developer Portal](https://developer.webex.com/)
    2. Create a Bot:
       - Navigate to My Webex Apps → Create a Bot
       - Fill in the bot details and click Add Bot
    3. Get your access token:
       - Copy the token shown after bot creation
       - Or regenerate via My Webex Apps → Edit Bot
  </Step>

  <Step title="Set your API keys">
    ```bash
    export WEBEX_ACCESS_TOKEN=xxx
    export OPENAI_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U webexpythonsdk openai agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/webex_tools.py
    ```

    ```bash Windows
    python cookbook/tools/webex_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps> 