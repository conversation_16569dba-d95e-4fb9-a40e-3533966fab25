---
title: Slack Tools
---

## Code

```python cookbook/tools/slack_tools.py
from agno.agent import Agent
from agno.tools.slack import SlackTools

agent = Agent(
    tools=[SlackTools()],
    show_tool_calls=True,
    markdown=True,
)
agent.print_response("Send a message to #general channel saying 'Hello from Agno!'")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export OPENAI_API_KEY=xxx
    ```
  </Step>

  <Step title="Set your Slack token">
    ```bash
    export SLACK_BOT_TOKEN=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U slack-sdk openai agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/slack_tools.py
    ```

    ```bash Windows
    python cookbook/tools/slack_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>