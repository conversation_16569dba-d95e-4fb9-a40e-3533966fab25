Description:

Implemented an AI agent using the agno library with PandasTools for automated data analysis.

The agent loads a CSV file (data.csv) and performs analysis based on natural language instructions.

Enables interaction with data without manual Pandas coding, simplifying data exploration and insights extraction.

Includes setup instructions for environment variables and dependencies.

---
title: Pandas Tools
---

## Code

```python cookbook/tools/pandas_tools.py
from agno.agent import Agent
from agno.tools.pandas import PandasTools

agent = Agent(
    tools=[PandasTools()],
    show_tool_calls=True,
    markdown=True,
)
agent.print_response("Load and analyze the dataset from data.csv")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export OPENAI_API_KEY=xxx
    ```
  </Step> 

  <Step title="Install libraries">
    ```bash
    pip install -U pandas openai agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/pandas_tools.py
    ```

    ```bash Windows
    python cookbook/tools/pandas_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>
