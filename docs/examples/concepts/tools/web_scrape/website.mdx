---
title: Website Tools
---

## Code

```python cookbook/tools/website_tools.py
from agno.agent import Agent
from agno.tools.website import WebsiteTools

agent = Agent(
    tools=[WebsiteTools()],
    show_tool_calls=True,
    markdown=True,
)
agent.print_response("Extract the main content from https://example.com")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export OPENAI_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U beautifulsoup4 requests openai agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/website_tools.py
    ```

    ```bash Windows
    python cookbook/tools/website_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>