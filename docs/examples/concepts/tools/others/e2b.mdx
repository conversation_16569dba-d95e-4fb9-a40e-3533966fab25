---
title: E2B Code Execution
description: Learn to use Agno's E2B integration to run your Agent-generated code in a secure sandbox.
---

## Code

```python cookbook/tools/e2b_tools.py
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.e2b import E2BTools

e2b_tools = E2BTools(
    timeout=600,  # 10 minutes timeout (in seconds)
    filesystem=True,
    internet_access=True,
    sandbox_management=True,
    command_execution=True,
)

agent = Agent(
    name="Code Execution Sandbox",
    agent_id="e2b-sandbox",
    model=OpenAIChat(id="gpt-4o"),
    tools=[e2b_tools],
    markdown=True,
    show_tool_calls=True,
    instructions=[
        "You are an expert at writing and validating Python code using a secure E2B sandbox environment.",
        "Your primary purpose is to:",
        "1. Write clear, efficient Python code based on user requests",
        "2. Execute and verify the code in the E2B sandbox",
        "3. Share the complete code with the user, as this is the main use case",
        "4. Provide thorough explanations of how the code works",
    ],
)

# Example: Generate Fibonacci numbers
agent.print_response(
    "Write Python code to generate the first 10 Fibonacci numbers and calculate their sum and average"
)

# Example: Data visualization
agent.print_response(
    "Write a Python script that creates a sample dataset of sales by region and visualize it with matplotlib"
)

# Example: Run a web server
agent.print_response(
    "Create a simple FastAPI web server that displays 'Hello from E2B Sandbox!' and run it to get a public URL"
)

# Example: Sandbox management
agent.print_response("What's the current status of our sandbox and how much time is left before timeout?")

# Example: File operations
agent.print_response("Create a text file with the current date and time, then read it back")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Create an E2B account">
    Create an account at [E2B](https://e2b.dev/) and get your API key from the dashboard.
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install e2b_code_interpreter
    ```
  </Step>

  <Step title="Set your API Key">
    <CodeGroup>
    ```bash Mac/Linux
    export E2B_API_KEY=your_api_key_here
    ```

    ```bash Windows (Command Prompt)
    set E2B_API_KEY=your_api_key_here
    ```

    ```bash Windows (PowerShell)
    $env:E2B_API_KEY="your_api_key_here"
    ```
    </CodeGroup>
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac/Linux
    python cookbook/tools/e2b_tools.py
    ```

    ```bash Windows
    python cookbook\tools\e2b_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>