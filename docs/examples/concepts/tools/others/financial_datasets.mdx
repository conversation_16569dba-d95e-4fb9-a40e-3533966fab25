---
title: Financial Datasets Tools
---

## Code

```python cookbook/tools/financial_datasets_tools.py
from agno.agent import Agent
from agno.tools.financial_datasets import FinancialDatasetsTools

agent = Agent(
    name="Financial Data Agent",
    tools=[
        FinancialDatasetsTools(),  # For accessing financial data
    ],
    description="You are a financial data specialist that helps analyze financial information for stocks and cryptocurrencies.",
    instructions=[
        "When given a financial query:",
        "1. Use appropriate Financial Datasets methods based on the query type",
        "2. Format financial data clearly and highlight key metrics",
        "3. For financial statements, compare important metrics with previous periods when relevant",
        "4. Calculate growth rates and trends when appropriate",
        "5. Handle errors gracefully and provide meaningful feedback",
    ],
    markdown=True,
    show_tool_calls=True,
)

# Example 1: Financial Statements
print("\n=== Income Statement Example ===")
agent.print_response(
    "Get the most recent income statement for AAPL and highlight key metrics",
    stream=True,
)

# Example 2: Balance Sheet Analysis
print("\n=== Balance Sheet Analysis Example ===")
agent.print_response(
    "Analyze the balance sheets for MSFT over the last 3 years. Focus on debt-to-equity ratio and cash position.",
    stream=True,
)

```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API credentials">
    ```bash
    export FINANCIAL_DATASETS_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
      python cookbook/tools/financial_datasets_tools.py
    ```

    ```bash Windows
      python cookbook/tools/financial_datasets_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps>