---
title: Google Maps Tools
---

## Code

```python cookbook/tools/google_maps_tools.py
from agno.agent import Agent
from agno.tools.google_maps import GoogleMapTools
from agno.tools.crawl4ai import Crawl4aiTools  # Optional: for enriching place data

agent = Agent(
    name="Maps API Demo Agent",
    tools=[
        GoogleMapTools(),
        Crawl4aiTools(max_length=5000),  # Optional: for scraping business websites
    ],
    description="Location and business information specialist for mapping and location-based queries.",
    markdown=True,
    show_tool_calls=True,
)

# Example 1: Business Search
print("\n=== Business Search Example ===")
agent.print_response(
    "Find me highly rated Indian restaurants in Phoenix, AZ with their contact details",
    markdown=True,
    stream=True,
)

# Example 2: Directions
print("\n=== Directions Example ===")
agent.print_response(
    """Get driving directions from 'Phoenix Sky Harbor Airport' to 'Desert Botanical Garden', 
    avoiding highways if possible""",
    markdown=True,
    stream=True,
)

# Example 3: Address Validation and Geocoding
print("\n=== Address Validation and Geocoding Example ===")
agent.print_response(
    """Please validate and geocode this address: 
    '1600 Amphitheatre Parkway, Mountain View, CA'""",
    markdown=True,
    stream=True,
)

# Example 4: Distance Matrix
print("\n=== Distance Matrix Example ===")
agent.print_response(
    """Calculate the travel time and distance between these locations in Phoenix:
    Origins: ['Phoenix Sky Harbor Airport', 'Downtown Phoenix']
    Destinations: ['Desert Botanical Garden', 'Phoenix Zoo']""",
    markdown=True,
    stream=True,
)

# Example 5: Location Analysis
print("\n=== Location Analysis Example ===")
agent.print_response(
    """Analyze this location in Phoenix:
    Address: '2301 N Central Ave, Phoenix, AZ 85004'
    Please provide:
    1. Exact coordinates
    2. Nearby landmarks
    3. Elevation data
    4. Local timezone""",
    markdown=True,
    stream=True,
)

# Example 6: Multi-mode Transit Comparison
print("\n=== Transit Options Example ===")
agent.print_response(
    """Compare different travel modes from 'Phoenix Convention Center' to 'Phoenix Art Museum':
    1. Driving
    2. Walking
    3. Transit (if available)
    Include estimated time and distance for each option.""",
    markdown=True,
    stream=True,
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API keys">
    ```bash
    export GOOGLE_MAPS_API_KEY=xxx
    export OPENAI_API_KEY=xxx
    ```
    Get your API key from the [Google Cloud Console](https://console.cloud.google.com/projectselector2/google/maps-apis/credentials)
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U openai googlemaps agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/tools/google_maps_tools.py
    ```

    ```bash Windows
    python cookbook/tools/google_maps_tools.py
    ```
    </CodeGroup>
  </Step>
</Steps> 