---
title: Basic Async
---

## Code

```python cookbook/agent_concepts/async/basic.py
import asyncio

from agno.agent import Agent
from agno.models.openai import OpenAIChat

agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="You help people with their health and fitness goals.",
    instructions=["Recipes should be under 5 ingredients"],
    markdown=True,
)
# -*- Print a response to the cli
asyncio.run(agent.aprint_response("Share a breakfast recipe.", stream=True))
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Install libraries">

    ```bash
    pip install -U openai agno
    ```

  </Step>

  <Step title="Run Agent">

    <CodeGroup>

    ```bash Mac
    python cookbook/agent_concepts/async/basic.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/async/basic.py
    ```

    </CodeGroup>

  </Step>

</Steps>
