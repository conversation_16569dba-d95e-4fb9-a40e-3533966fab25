---
title: Persistent Memory with SQLite
---

This example shows how to use the Memory class to create a persistent memory.

Every time you run this, the `Memory` object will be re-initialized from the DB.

## Code

```python cookbook/agent_concepts/memory/02_persistent_memory.py
from typing import List

from agno.memory.v2.db.schema import MemoryRow
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.memory.v2.schema import UserMemory

memory_db = SqliteMemoryDb(table_name="memory", db_file="tmp/memory.db")
memory = Memory(db=memory_db)

john_doe_id = "<EMAIL>"

# Run 1
memory.add_user_memory(
    memory=UserMemory(memory="The user's name is <PERSON>", topics=["name"]),
    user_id=john_doe_id,
)

# Run this the 2nd time
# memory.add_user_memory(
#     memory=UserMemory(memory="The user works at a softward company called Agno", topics=["name"]),
#     user_id=john_doe_id,
# )


memories: List[MemoryRow] = memory_db.read_memories()
print("All the DB memories:")
for i, m in enumerate(memories):
    print(f"{i}: {m.memory['memory']} ({m.last_updated})")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Install libraries">
    ```bash
    pip install -U agno
    ```
  </Step>

  <Step title="Run Example">
    <CodeGroup>
    ```bash Mac
    python cookbook/agent_concepts/memory/02_persistent_memory.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/memory/02_persistent_memory.py
    ```
    </CodeGroup>
  </Step>
</Steps>
