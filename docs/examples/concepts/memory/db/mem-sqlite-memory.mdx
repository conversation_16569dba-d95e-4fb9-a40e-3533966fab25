---
title: SQLite Memory Storage
---

## Code

```python cookbook/agent_concepts/memory/sqlite_memory.py
"""
This example shows how to use the Memory class with SQLite storage.
"""

from agno.agent.agent import Agent
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage

# Create SQLite memory database
memory_db = SqliteMemoryDb(
    table_name="agent_memories",  # Table name to use in the database
    db_file="tmp/memory.db",      # Path to SQLite database file
)

# Create memory instance with SQLite backend
memory = Memory(db=memory_db)

# This will create the table if it doesn't exist
memory.clear()

# Session and user identifiers
session_id = "sqlite_memories"
user_id = "sqlite_user"

# Create agent with memory and SQLite storage
agent = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    memory=memory,
    storage=SqliteStorage(
        table_name="agent_sessions", 
        db_file="tmp/memory.db"
    ),
    enable_user_memories=True,
    enable_session_summaries=True,
)

# First interaction - introducing personal information
agent.print_response(
    "My name is <PERSON> and I like to hike in the mountains on weekends.",
    stream=True,
    user_id=user_id,
    session_id=session_id,
)

# Second interaction - testing if memory was stored
agent.print_response(
    "What are my hobbies?", 
    stream=True, 
    user_id=user_id, 
    session_id=session_id
)

# Display the memories stored in SQLite
memories = memory.get_user_memories(user_id=user_id)
print("Memories stored in SQLite:")
for i, m in enumerate(memories):
    print(f"{i}: {m.memory}")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set environment variables">
    ```bash
    export OPENAI_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U agno openai
    ```
  </Step>

  <Step title="Run Example">
    <CodeGroup>
    ```bash Mac/Linux
    python cookbook/agent_concepts/memory/sqlite_memory.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/memory/sqlite_memory.py
    ```
    </CodeGroup>
  </Step>
</Steps>