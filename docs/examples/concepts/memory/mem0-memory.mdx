---
title: Mem0 Memory
---

## Code

```python cookbook/agent_concepts/memory/mem0_memory.py
from agno.agent import Agent, RunResponse
from agno.models.openai import OpenAIChat
from agno.utils.pprint import pprint_run_response
from mem0 import MemoryClient

client = MemoryClient()

user_id = "agno"
messages = [
    {"role": "user", "content": "My name is <PERSON>."},
    {"role": "user", "content": "I live in NYC."},
    {"role": "user", "content": "I'm going to a concert tomorrow."},
]
# Comment out the following line after running the script once
client.add(messages, user_id=user_id)

agent = Agent(
    model=OpenAIChat(),
    context={"memory": client.get_all(user_id=user_id)},
    add_context=True,
)
run: RunResponse = agent.run("What do you know about me?")

pprint_run_response(run)

messages = [{"role": i.role, "content": str(i.content)} for i in (run.messages or [])]
client.add(messages, user_id=user_id)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export OPENAI_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U openai mem0 agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/agent_concepts/memory/mem0_memory.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/memory/mem0_memory.py
    ```
    </CodeGroup>
  </Step>
</Steps>