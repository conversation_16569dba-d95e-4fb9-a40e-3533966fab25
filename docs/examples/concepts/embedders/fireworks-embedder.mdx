---
title: Fireworks Embedder
---

## Code

```python
from agno.agent import AgentKnowledge
from agno.embedder.fireworks import FireworksEmbedder
from agno.vectordb.pgvector import PgVector

embeddings = FireworksEmbedder().get_embedding(
    "The quick brown fox jumps over the lazy dog."
)

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Example usage:
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="fireworks_embeddings",
        embedder=FireworksEmbedder(),
    ),
    num_documents=2,
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export FIREWORKS_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U sqlalchemy 'psycopg[binary]' pgvector fireworks-ai agno
    ```
  </Step>

  <Step title="Run PgVector">
    ```bash
    docker run -d \
      -e POSTGRES_DB=ai \
      -e POSTGRES_USER=ai \
      -e POSTGRES_PASSWORD=ai \
      -e PGDATA=/var/lib/postgresql/data/pgdata \
      -v pgvolume:/var/lib/postgresql/data \
      -p 5532:5432 \
      --name pgvector \
      agnohq/pgvector:16
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/agent_concepts/knowledge/embedders/fireworks_embedder.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/knowledge/embedders/fireworks_embedder.py
    ```
    </CodeGroup>
  </Step>
</Steps>