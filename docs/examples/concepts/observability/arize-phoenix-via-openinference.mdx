---
title: Arize Phoenix via OpenInference
---

## Overview

This example demonstrates how to instrument your Agno agent with OpenInference and send traces to Arize Phoenix.

## Code

```python
import asyncio
import os

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.yfinance import YFinanceTools
from phoenix.otel import register

# Set environment variables for Arize Phoenix
os.environ["PHOENIX_CLIENT_HEADERS"] = f"api_key={os.getenv('ARIZE_PHOENIX_API_KEY')}"
os.environ["PHOENIX_COLLECTOR_ENDPOINT"] = "https://app.phoenix.arize.com"

# Configure the Phoenix tracer
tracer_provider = register(
    project_name="agno-stock-price-agent",  # Default is 'default'
    auto_instrument=True,  # Automatically use the installed OpenInference instrumentation
)

# Create and configure the agent
agent = Agent(
    name="Stock Price Agent",
    model=OpenAIChat(id="gpt-4o-mini"),
    tools=[YFinanceTools()],
    instructions="You are a stock price agent. Answer questions in the style of a stock analyst.",
    debug_mode=True,
)

# Use the agent
agent.print_response("What is the current price of Tesla?")
```

## Usage

<Steps>
  <Step title="Install Dependencies">
    ```bash
    pip install agno arize-phoenix openai openinference-instrumentation-agno opentelemetry-sdk opentelemetry-exporter-otlp
    ```
  </Step>

  <Step title="Set Environment Variables">
    ```bash
    export ARIZE_PHOENIX_API_KEY=<your-key>
    ```
  </Step>

  <Step title="Run the Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/observability/arize_phoenix_via_openinference.py
    ```

    ```bash Windows
    python cookbook/observability/arize_phoenix_via_openinference.py
    ```
    </CodeGroup>
  </Step>
</Steps>