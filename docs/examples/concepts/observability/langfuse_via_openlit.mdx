---
title: <PERSON><PERSON> via OpenLIT
---

## Overview

This example demonstrates how to use Lang<PERSON> via OpenLIT to trace model calls.

## Code

```python
import base64
import os

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools

LANGFUSE_AUTH = base64.b64encode(
    f"{os.getenv('LANGFUSE_PUBLIC_KEY')}:{os.getenv('LANGFUSE_SECRET_KEY')}".encode()
).decode()

os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = (
    "https://us.cloud.langfuse.com/api/public/otel"  # 🇺🇸 US data region
)
# os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"]="https://cloud.langfuse.com/api/public/otel" # 🇪🇺 EU data region
# os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"]="http://localhost:3000/api/public/otel" # 🏠 Local deployment (>= v3.22.0)

os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {LANGFUSE_AUTH}"

from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor

trace_provider = TracerProvider()
trace_provider.add_span_processor(SimpleSpanProcessor(OTLPSpanExporter()))

# Sets the global default tracer provider
from opentelemetry import trace

trace.set_tracer_provider(trace_provider)

# Creates a tracer from the global tracer provider
tracer = trace.get_tracer(__name__)

import openlit

# Initialize OpenLIT instrumentation. The disable_batch flag is set to true to process traces immediately.
openlit.init(tracer=tracer, disable_batch=True)

agent = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    tools=[DuckDuckGoTools()],
    markdown=True,
    debug_mode=True,
)

agent.print_response("What is currently trending on Twitter?")
```

## Usage

<Steps>
  <Step title="Install Dependencies">
    ```bash
    pip install agno openai langfuse openlit opentelemetry-sdk opentelemetry-exporter-otlp
    ```
  </Step>

  <Step title="Set Environment Variables">
    ```bash
    export LANGFUSE_PUBLIC_KEY=<your-public-key>
    export LANGFUSE_SECRET_KEY=<your-secret-key>
    ```
  </Step>

  <Step title="Run the Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/observability/langfuse_via_openlit.py
    ```

    ```bash Windows
    python cookbook/observability/langfuse_via_openlit.py
    ```
    </CodeGroup>
  </Step>
</Steps>

## Notes

- **Data Regions**: Adjust the `OTEL_EXPORTER_OTLP_ENDPOINT` for your data region or local deployment as needed:
  - `https://us.cloud.langfuse.com/api/public/otel` for the US region
  - `https://cloud.langfuse.com/api/public/otel` for the EU region
  - `http://localhost:3000/api/public/otel` for local deployment