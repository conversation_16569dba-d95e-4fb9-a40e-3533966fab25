---
title: <PERSON><PERSON><PERSON>
---

## Overview

This example demonstrates how to instrument your Agno agent with OpenInference and send traces to LangSmith.

## Code

```python
import os

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools
from openinference.instrumentation.agno import AgnoInstrumentor
from opentelemetry import trace as trace_api
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor

# Set the endpoint and headers for Lang<PERSON><PERSON>
endpoint = "https://eu.api.smith.langchain.com/otel/v1/traces"
headers = {
    "x-api-key": os.getenv("LANGSMITH_API_KEY"),
    "Langsmith-Project": os.getenv("LANGSMITH_PROJECT"),
}

# Configure the tracer provider
tracer_provider = TracerProvider()
tracer_provider.add_span_processor(
    SimpleSpanProcessor(OTLPSpanExporter(endpoint=endpoint, headers=headers))
)
trace_api.set_tracer_provider(tracer_provider=tracer_provider)

# Start instrumenting agno
AgnoInstrumentor().instrument()

# Create and configure the agent
agent = Agent(
    name="Stock Market Agent",
    model=OpenAIChat(id="gpt-4o-mini"),
    tools=[DuckDuckGoTools()],
    markdown=True,
    debug_mode=True,
)

# Use the agent
agent.print_response("What is news on the stock market?")
```

## Usage

<Steps>
  <Step title="Install Dependencies">
    ```bash
    pip install agno openai openinference-instrumentation-agno opentelemetry-sdk opentelemetry-exporter-otlp
    ```
  </Step>

  <Step title="Set Environment Variables">
    ```bash
    export LANGSMITH_API_KEY=<your-key>
    export LANGSMITH_TRACING=true
    export LANGSMITH_ENDPOINT=https://eu.api.smith.langchain.com  # or https://api.smith.langchain.com for US
    export LANGSMITH_PROJECT=<your-project-name>
    ```
  </Step>

  <Step title="Run the Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/observability/langsmith_via_openinference.py
    ```

    ```bash Windows
    python cookbook/observability/langsmith_via_openinference.py
    ```
    </CodeGroup>
  </Step>
</Steps>

## Notes

- **Data Regions**: Choose the appropriate `LANGSMITH_ENDPOINT` based on your data region.