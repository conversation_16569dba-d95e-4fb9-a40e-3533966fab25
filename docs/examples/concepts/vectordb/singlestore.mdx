---
title: SingleStore Integration
---

## Code

```python cookbook/agent_concepts/vector_dbs/singlestore.py
from os import getenv

from agno.agent import Agent
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.singlestore import SingleStore
from sqlalchemy.engine import create_engine

USERNAME = getenv("SINGLESTORE_USERNAME")
PASSWORD = getenv("SINGLESTORE_PASSWORD")
HOST = getenv("SINGLESTORE_HOST")
PORT = getenv("SINGLESTORE_PORT")
DATABASE = getenv("SINGLESTORE_DATABASE")
SSL_CERT = getenv("SINGLESTORE_SSL_CERT", None)

db_url = (
    f"mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}?charset=utf8mb4"
)
if SSL_CERT:
    db_url += f"&ssl_ca={SSL_CERT}&ssl_verify_cert=true"

db_engine = create_engine(db_url)

knowledge_base = PDFUrlKnowledgeBase(
    urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
    vector_db=SingleStore(
        collection="recipes",
        db_engine=db_engine,
        schema=DATABASE,
    ),
)

knowledge_base.load(recreate=False)

agent = Agent(
    knowledge=knowledge_base,
    show_tool_calls=True,
    search_knowledge=True,
    read_chat_history=True,
)

agent.print_response("How do I make pad thai?", markdown=True)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set environment variables">
    ```bash
    export SINGLESTORE_HOST="localhost"
    export SINGLESTORE_PORT="3306"
    export SINGLESTORE_USERNAME="root"
    export SINGLESTORE_PASSWORD="admin"
    export SINGLESTORE_DATABASE="AGNO"
    export SINGLESTORE_SSL_CA=".certs/singlestore_bundle.pem"
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U sqlalchemy pymysql pypdf openai agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/agent_concepts/vector_dbs/singlestore.py
    ```

    ```bash Windows
    python cookbook/agent_concepts/vector_dbs/singlestore.py
    ```
    </CodeGroup>
  </Step>
</Steps>