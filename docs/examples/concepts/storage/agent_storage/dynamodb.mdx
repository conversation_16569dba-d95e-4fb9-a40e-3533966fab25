---
title: DynamoDB Agent Storage
sidebarTitle: DynamoDB
---

<PERSON><PERSON> supports using DynamoDB as a storage backend for Agents using the `DynamoDbStorage` class.

## Usage

You need to provide `aws_access_key_id` and `aws_secret_access_key` parameters to the `DynamoDbStorage` class.

```python dynamodb_storage_for_agent.py
from agno.storage.dynamodb import DynamoDbStorage

# AWS Credentials
AWS_ACCESS_KEY_ID = getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = getenv("AWS_SECRET_ACCESS_KEY")

storage = DynamoDbStorage(
    # store sessions in the ai.sessions table
    table_name="agent_sessions",
    # region_name: DynamoDB region name
    region_name="us-east-1",
    # aws_access_key_id: AWS access key id
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    # aws_secret_access_key: AWS secret access key
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
)

# Add storage to the Agent
agent = Agent(storage=storage)
```

## Params

<Snippet file="storage-dynamodb-params.mdx" />

## Developer Resources

- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/storage/dynamodb_storage/dynamodb_storage_for_agent.py)
