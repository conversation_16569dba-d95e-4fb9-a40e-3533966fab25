---
title: Agent with Tools
---

## Code

```python cookbook/models/azure/ai_foundry/tool_use.py
from agno.agent import Agent
from agno.models.azure import AzureAIFoundry
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=AzureAIFoundry(id="Cohere-command-r-08-2024"),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

agent.print_response("Whats happening in France?", stream=True)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export AZURE_API_KEY=xxx
    export AZURE_ENDPOINT=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U azure-ai-inference agno duckduckgo-search
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/azure/ai_foundry/tool_use.py
    ```

    ```bash Windows
    python cookbook/models/azure/ai_foundry/tool_use.py
    ```
    </CodeGroup>
  </Step>
</Steps>