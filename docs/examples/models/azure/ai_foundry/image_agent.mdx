---
title: Agent with Image Input
---

Azure AI Foundry supports image input with models like `Llama-3.2-11B-Vision-Instruct`. You can use this to analyze images and get information about them.

## Code

```python cookbook/models/azure/ai_foundry/image_agent.py
from pathlib import Path
from agno.agent import Agent
from agno.media import Image
from agno.models.azure import AzureAIFoundry

agent = Agent(
    model=AzureAIFoundry(id="Llama-3.2-11B-Vision-Instruct"),
    markdown=True,
)

image_path = Path(__file__).parent.joinpath("sample.jpg")

# Read the image file content as bytes
with open(image_path, "rb") as img_file:
    image_bytes = img_file.read()

agent.print_response(
    "Tell me about this image.",
    images=[
        Image(content=image_bytes),
    ],
    stream=True,
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export AZURE_API_KEY=xxx
    export AZURE_ENDPOINT=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U azure-ai-inference agno
    ```
  </Step>

  <Step title="Add an Image">
    Place an image file named `sample.jpg` in the same directory as your script.
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/azure/ai_foundry/image_agent.py
    ```

    ```bash Windows
    python cookbook/models/azure/ai_foundry/image_agent.py
    ```
    </CodeGroup>
  </Step>
</Steps> 