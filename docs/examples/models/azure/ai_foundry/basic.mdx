---
title: Basic Agent
---

## Code

```python cookbook/models/azure/ai_foundry/basic.py
from agno.agent import Agent, RunResponse  # noqa
from agno.models.azure import AzureAIFoundry

agent = Agent(model=AzureAIFoundry(id="Phi-4"), markdown=True)

# Get the response in a variable
# run: RunResponse = agent.run("Share a 2 sentence horror story")
# print(run.content)

# Print the response on the terminal
agent.print_response("Share a 2 sentence horror story")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export AZURE_API_KEY=xxx
    export AZURE_ENDPOINT=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U azure-ai-inference agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/azure/ai_foundry/basic.py
    ```

    ```bash Windows
    python cookbook/models/azure/ai_foundry/basic.py
    ```
    </CodeGroup>
  </Step>
</Steps>