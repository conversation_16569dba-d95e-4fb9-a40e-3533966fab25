---
title: Basic Agent
---

## Code

```python cookbook/models/anthropic/basic.py
from agno.agent import Agent, RunResponse  # noqa
from agno.models.anthropic import Claude

agent = Agent(model=<PERSON>(id="claude-3-5-sonnet-20241022"), markdown=True)

# Get the response in a variable
# run: RunResponse = agent.run("Share a 2 sentence horror story")
# print(run.content)

# Print the response in the terminal
agent.print_response("Share a 2 sentence horror story")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export ANTHROPIC_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U anthropic agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/anthropic/basic.py
    ```

    ```bash Windows
    python cookbook/models/anthropic/basic.py
    ```
    </CodeGroup>
  </Step>
</Steps>