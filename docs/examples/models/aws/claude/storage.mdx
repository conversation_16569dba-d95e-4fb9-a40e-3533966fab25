---
title: Agent with Storage
---

## Code

```python cookbook/models/aws/claude/storage.py
from agno.agent import Agent
from agno.models.aws import Claude
from agno.storage.postgres import PostgresStorage
from agno.tools.duckduckgo import DuckDuckGoTools

db_url = "postgresql+psycopg://ai:ai@localhost:5532/ai"

agent = Agent(
    model=Claude(id="anthropic.claude-3-5-sonnet-20240620-v1:0"),
    storage=PostgresStorage(table_name="agent_sessions", db_url=db_url),
    tools=[DuckDuckGoTools()],
    add_history_to_messages=True,
)
agent.print_response("How many people live in Canada?")
agent.print_response("What is their national anthem called?")
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your AWS Credentials">
    ```bash
    export AWS_ACCESS_KEY_ID=***
    export AWS_SECRET_ACCESS_KEY=***
    export AWS_REGION=***
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U anthropic[bedrock] duckduckgo-search sqlalchemy psycopg agno
    ```
  </Step>

  <Step title="Run PgVector">
    ```bash
    docker run -d \
      -e POSTGRES_DB=ai \
      -e POSTGRES_USER=ai \
      -e POSTGRES_PASSWORD=ai \
      -e PGDATA=/var/lib/postgresql/data/pgdata \
      -v pgvolume:/var/lib/postgresql/data \
      -p 5532:5432 \
      --name pgvector \
      agnohq/pgvector:16
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/aws/claude/storage.py
    ```

    ```bash Windows
    python cookbook/models/aws/claude/storage.py
    ```
    </CodeGroup>
  </Step>
</Steps>