---
title: Streaming Agent
---

## Code

```python cookbook/models/aws/claude/basic_stream.py
from typing import Iterator  # noqa
from agno.agent import Agent, RunResponse  # noqa
from agno.models.aws import Claude

agent = Agent(
    model=<PERSON>(id="anthropic.claude-3-5-sonnet-20240620-v1:0"), markdown=True
)

# Get the response in a variable
# run_response: Iterator[RunResponse] = agent.run("Share a 2 sentence horror story", stream=True)
# for chunk in run_response:
#     print(chunk.content)

# Print the response in the terminal
agent.print_response("Share a 2 sentence horror story", stream=True)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your AWS Credentials">
    ```bash
    export AWS_ACCESS_KEY_ID=***
    export AWS_SECRET_ACCESS_KEY=***
    export AWS_REGION=***
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U anthropic[bedrock] agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/aws/claude/basic_stream.py
    ```

    ```bash Windows
    python cookbook/models/aws/claude/basic_stream.py
    ```
    </CodeGroup>
  </Step>
</Steps>