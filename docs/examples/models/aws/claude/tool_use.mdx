---
title: Agent with Tools
---

## Code

```python cookbook/models/aws/claude/tool_use.py
from agno.agent import Agent
from agno.models.aws import Claude
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=<PERSON>(id="anthropic.claude-3-5-sonnet-20240620-v1:0"),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)
agent.print_response("Whats happening in France?", stream=True)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your AWS Credentials">
    ```bash
    export AWS_ACCESS_KEY_ID=***
    export AWS_SECRET_ACCESS_KEY=***
    export AWS_REGION=***
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U anthropic[bedrock] duckduckgo-search agno
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/aws/claude/tool_use.py
    ```

    ```bash Windows
    python cookbook/models/aws/claude/tool_use.py
    ```
    </CodeGroup>
  </Step>
</Steps>