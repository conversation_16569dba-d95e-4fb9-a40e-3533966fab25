---
title: Agent with Image Input
---

AWS Bedrock supports image input with models like `amazon.nova-pro-v1:0`. You can use this to analyze images and get information about them.

## Code

```python cookbook/models/aws/bedrock/image_agent.py
from pathlib import Path
from agno.agent import Agent
from agno.media import Image
from agno.models.aws import AwsBedrock
from agno.tools.duckduckgo import DuckDuckGoTools

agent = Agent(
    model=AwsBedrock(id="amazon.nova-pro-v1:0"),
    tools=[DuckDuckGoTools()],
    markdown=True,
)

image_path = Path(__file__).parent.joinpath("sample.jpg")

# Read the image file content as bytes
with open(image_path, "rb") as img_file:
    image_bytes = img_file.read()

agent.print_response(
    "Tell me about this image and give me the latest news about it.",
    images=[
        Image(content=image_bytes, format="jpeg"),
    ],
)
```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your AWS Credentials">
    ```bash
    export AWS_ACCESS_KEY_ID=***
    export AWS_SECRET_ACCESS_KEY=***
    export AWS_REGION=***
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U boto3 duckduckgo-search agno
    ```
  </Step>

  <Step title="Add an Image">
    Place an image file named `sample.jpg` in the same directory as your script.
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/models/aws/bedrock/image_agent.py
    ```

    ```bash Windows
    python cookbook/models/aws/bedrock/image_agent.py
    ```
    </CodeGroup>
  </Step>
</Steps>
