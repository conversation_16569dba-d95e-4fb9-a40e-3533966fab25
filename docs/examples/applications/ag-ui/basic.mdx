---
title: Basic
description: Expose your Agno Agent as a AG-UI compatible app
---

## Code

```python cookbook/apps/agui/basic.py
from agno.agent.agent import Agent
from agno.app.agui.app import AGUIApp
from agno.models.openai import OpenAIChat

chat_agent = Agent(
    name="Assistant",
    model=OpenAIChat(id="gpt-4o"),
    instructions="You are a helpful AI assistant.",
    add_datetime_to_instructions=True,
    markdown=True,
)

agui_app = AGUIApp(
    agent=chat_agent,
    name="Basic AG-UI Agent",
    app_id="basic_agui_agent",
    description="A basic agent that demonstrates AG-UI protocol integration.",
)

app = agui_app.get_app()

if __name__ == "__main__":
    agui_app.serve(app="basic:app", port=8000, reload=True)
```

You can see instructions on how to setup an AG-UI compatible front-end to use this with in the [AG-UI App](/applications/ag-ui/introduction) page.

You can also check the [CopilotKit docs](https://docs.copilotkit.ai/agno) on working with <PERSON><PERSON>, to learn more on how to build the UI side.