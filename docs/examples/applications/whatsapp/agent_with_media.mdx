---
title: Agent With Media
---

This example shows how to use the files with whatsapp app.

## Code

```python cookbook/apps/whatsapp/agent_with_media.py
from agno.agent import Agent
from agno.app.whatsapp.app import WhatsappAPI
from agno.models.google import Gemini

media_agent = Agent(
    name="Media Agent",
    model=Gemini(id="gemini-2.0-flash"),
    add_history_to_messages=True,
    num_history_responses=3,
    add_datetime_to_instructions=True,
    markdown=True,
)

whatsapp_app = WhatsappAPI(
    agent=media_agent,
    name="Media Agent",
    app_id="media_agent",
    description="A agent that can send media to the user.",
)

app = whatsapp_app.get_app()

if __name__ == "__main__":
    whatsapp_app.serve(app="agent_with_media:app", port=8000, reload=True)

```

## Usage

<Steps>
  <Snippet file="create-venv-step.mdx" />

  <Step title="Set your API key">
    ```bash
    export GOOGLE_API_KEY=xxx
    ```
  </Step>

  <Step title="Install libraries">
    ```bash
    pip install -U agno google-generativeai "uvicorn[standard]"
    ```
  </Step>

  <Step title="Run Agent">
    <CodeGroup>
    ```bash Mac
    python cookbook/apps/whatsapp/agent_with_media.py
    ```

    ```bash Windows
    python cookbook/apps/whatsapp/agent_with_media.py
    ```
    </CodeGroup>
  </Step>
</Steps> 