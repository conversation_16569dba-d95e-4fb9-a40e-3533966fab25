---
title: Introduction
---

This guide walks through the basics of building Agents with Agno.

The examples build on each other, introducing new concepts and capabilities progressively. Each example contains detailed comments, example prompts, and required dependencies.

## Setup

Create a virtual environment:

```bash
python3 -m venv .venv
source .venv/bin/activate
```

Install the required dependencies:

```bash
pip install openai duckduckgo-search yfinance lancedb tantivy pypdf requests exa-py newspaper4k lxml_html_clean sqlalchemy agno
```

Export your OpenAI API key:

```bash
export OPENAI_API_KEY=your_api_key
```

## Examples

<CardGroup cols={3}>
  <Card title="Basic Agent" icon="robot" iconType="duotone" href="./basic-agent">
    Build a news reporter with a vibrant personality. This Agent only shows basic LLM inference.
  </Card>

  <Card title="Agent with Tools" icon="toolbox" iconType="duotone" href="./agent-with-tools">
    Add web search capabilities using DuckDuckGo for real-time information gathering.
  </Card>

  <Card title="Agent with Knowledge" icon="brain" iconType="duotone" href="./agent-with-knowledge">
    Add a vector database to your agent to store and search knowledge.
  </Card>

  <Card title="Agent with Storage" icon="database" iconType="duotone" href="./agent-with-storage">
    Add persistence to your agents with session management and history capabilities.
  </Card>

  <Card title="Agent Team" icon="users" iconType="duotone" href="./agent-team">
    Create an agent team specializing in market research and financial analysis.
  </Card>

  <Card title="Structured Output" icon="code" iconType="duotone" href="./structured-output">
    Generate a structured output using a Pydantic model.
  </Card>

  <Card title="Custom Tools" icon="wrench" iconType="duotone" href="./custom-tools">
    Create and integrate custom tools with your agent.
  </Card>

  <Card title="Research Agent" icon="magnifying-glass" iconType="duotone" href="./research-agent">
    Build an AI research agent using Exa with controlled output steering.
  </Card>

  <Card title="Research Workflow" icon="diagram-project" iconType="duotone" href="./research-workflow">
    Create a research workflow combining web searches and content scraping.
  </Card>

  <Card title="Image Agent" icon="image" iconType="duotone" href="./image-agent">
    Create an agent that can understand images.
  </Card>

  <Card title="Image Generation" icon="paintbrush" iconType="duotone" href="./image-generation">
    Create an Agent that can generate images using DALL-E.
  </Card>

  <Card title="Video Generation" icon="video" iconType="duotone" href="./video-generation">
    Create an Agent that can generate videos using ModelsLabs.
  </Card>

  <Card title="Audio Agent" icon="microphone" iconType="duotone" href="./audio-agent">
    Create an Agent that can process audio input and generate responses.
  </Card>

  <Card title="Agent with State" icon="database" iconType="duotone" href="./agent-state">
    Create an Agent with session state management.
  </Card>

  <Card title="Agent Context" icon="sitemap" iconType="duotone" href="./agent-context">
    Evaluate dependencies at agent.run and inject them into the instructions.
  </Card>

  <Card title="Agent Session" icon="clock-rotate-left" iconType="duotone" href="./agent-session">
    Create an Agent with persistent session memory across conversations.
  </Card>

  <Card title="User Memories" icon="memory" iconType="duotone" href="./user-memories">
    Create an Agent that stores user memories and summaries.
  </Card>

  <Card title="Function Retries" icon="rotate" iconType="duotone" href="./retry-functions">
    Handle function retries for failed or unsatisfactory outputs.
  </Card>

  <Card title="Human in the Loop" icon="user-check" iconType="duotone" href="./human-in-the-loop">
    Add user confirmation and safety checks for interactive agent control.
  </Card>
</CardGroup>

Each example includes runnable code and detailed explanations. We recommend following them in order, as concepts build upon previous examples.
