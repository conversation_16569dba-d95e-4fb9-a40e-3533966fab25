---
title: Hugging<PERSON>ace Embedder
sidebarTitle: HuggingFace
---

The `HuggingfaceCustomEmbedder` class is used to embed text data into vectors using the Hugging Face API. You can get one from [here](https://huggingface.co/settings/tokens).

## Usage

```python cookbook/embedders/huggingface_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.huggingface import HuggingfaceCustomEmbedder

# Embed sentence in database
embeddings = HuggingfaceCustomEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="huggingface_embeddings",
        embedder=HuggingfaceCustomEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter            | Type                       | Default            | Description                                                  |
| -------------------- | -------------------------- | ------------------ | ------------------------------------------------------------ |
| `dimensions`         | `int`                      | -                  | The dimensionality of the generated embeddings               |
| `model`              | `str`                      | `all-MiniLM-L6-v2` | The name of the HuggingFace model to use                     |
| `api_key`            | `str`                      | -                  | The API key used for authenticating requests                 |
| `client_params`      | `Optional[Dict[str, Any]]` | -                  | Optional dictionary of parameters for the HuggingFace client |
| `huggingface_client` | `Any`                      | -                  | Optional pre-configured HuggingFace client instance          |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/huggingface_embedder.py)
