---
title: OpenAI Embedder
sidebarTitle: OpenAI
---

<PERSON><PERSON> uses the `OpenAIEmbedder` as the default embeder for the vector database. The `OpenAIEmbedder` class is used to embed text data into vectors using the OpenAI API. Get your key from [here](https://platform.openai.com/api-keys).

## Usage

```python cookbook/embedders/openai_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.openai import OpenAIEmbedder

# Embed sentence in database
embeddings = OpenAIEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="openai_embeddings",
        embedder=OpenAIEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter         | Type                         | Default                    | Description                                                                      |
| ----------------- | ---------------------------- | -------------------------- | -------------------------------------------------------------------------------- |
| `model`           | `str`                        | `"text-embedding-ada-002"` | The name of the model used for generating embeddings.                            |
| `dimensions`      | `int`                        | `1536`                     | The dimensionality of the embeddings generated by the model.                     |
| `encoding_format` | `Literal['float', 'base64']` | `"float"`                  | The format in which the embeddings are encoded. Options are "float" or "base64". |
| `user`            | `str`                        | -                          | The user associated with the API request.                                        |
| `api_key`         | `str`                        | -                          | The API key used for authenticating requests.                                    |
| `organization`    | `str`                        | -                          | The organization associated with the API request.                                |
| `base_url`        | `str`                        | -                          | The base URL for the API endpoint.                                               |
| `request_params`  | `Optional[Dict[str, Any]]`   | -                          | Additional parameters to include in the API request.                             |
| `client_params`   | `Optional[Dict[str, Any]]`   | -                          | Additional parameters for configuring the API client.                            |
| `openai_client`   | `Optional[OpenAIClient]`     | -                          | An instance of the OpenAIClient to use for making API requests.                  |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/openai_embedder.py)
