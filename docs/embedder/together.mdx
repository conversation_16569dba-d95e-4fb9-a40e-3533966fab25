---
title: Together Embedder
sidebarTitle: Together
---

The `TogetherEmbedder` can be used to embed text data into vectors using the Together API. Together uses the OpenAI API specification, so the `TogetherEmbedder` class is similar to the `OpenAIEmbedder` class, incorporating adjustments to ensure compatibility with the Together platform. Get your key from [here](https://api.together.xyz/settings/api-keys).

## Usage

```python cookbook/embedders/together_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.together import TogetherEmbedder

# Embed sentence in database
embeddings = TogetherEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="together_embeddings",
        embedder=TogetherEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter    | Type  | Default                                  | Description                                                  |
| ------------ | ----- | ---------------------------------------- | ------------------------------------------------------------ |
| `model`      | `str` | `"nomic-ai/nomic-embed-text-v1.5"`       | The name of the model used for generating embeddings.        |
| `dimensions` | `int` | `768`                                    | The dimensionality of the embeddings generated by the model. |
| `api_key`    | `str` |                                          | The API key used for authenticating requests.                |
| `base_url`   | `str` | `"https://api.Together.ai/inference/v1"` | The base URL for the API endpoint.                           |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/together_embedder.py)
