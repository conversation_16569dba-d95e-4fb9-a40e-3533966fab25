---
title: Cohere Embedder
sidebarTitle: Cohere
---

The `CohereEmbedder` class is used to embed text data into vectors using the Cohere API. You can get started with Cohere from [here](https://docs.cohere.com/reference/about)

Get your key from [here](https://dashboard.cohere.com/api-keys).

## Usage

```python cookbook/embedders/cohere_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.cohere import CohereEmbedder

# Add embedding to database
embeddings = CohereEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")
# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="cohere_embeddings",
        embedder=CohereEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter        | Type                       | Default           | Description                                                                |
| ---------------- | -------------------------- | ----------------- | -------------------------------------------------------------------------- |
| `model`          | `str`                      | `"embed-english-v3.0"` | The name of the model used for generating embeddings.                 |
| `input_type`     | `str`                      | `search_query`    | The type of input to embed. You can find more details [here](https://docs.cohere.com/docs/embeddings#the-input_type-parameter) |
| `embedding_types`| `Optional[List[str]]`      | -                 | The type of embeddings to generate. Optional.                              |
| `api_key`        | `str`                      | -                 | The Cohere API key used for authenticating requests.                              |
| `request_params` | `Optional[Dict[str, Any]]` | -                 | Additional parameters to include in the API request. Optional.             |
| `client_params`  | `Optional[Dict[str, Any]]` | -                 | Additional parameters for configuring the API client. Optional.            |
| `cohere_client`  | `Optional[CohereClient]`   | -                 | An instance of the CohereClient to use for making API requests. Optional.  |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/cohere_embedder.py)
