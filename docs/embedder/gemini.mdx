---
title: Gemini Embedder
sidebarTitle: Gemini
---

The `GeminiEmbedder` class is used to embed text data into vectors using the Gemini API. You can get one from [here](https://ai.google.dev/aistudio).

## Usage

```python cookbook/embedders/gemini_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.google import GeminiEmbedder

# Embed sentence in database
embeddings = GeminiEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="gemini_embeddings",
        embedder=<PERSON><PERSON><PERSON>dder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter        | Type                       | Default                     | Description                                                 |
| ---------------- | -------------------------- | --------------------------- | ----------------------------------------------------------- |
| `dimensions`     | `int`                      | `768`                       | The dimensionality of the generated embeddings              |
| `model`          | `str`                      | `models/text-embedding-004` | The name of the Gemini model to use                         |
| `task_type`      | `str`                      | -                           | The type of task for which embeddings are being generated   |
| `title`          | `str`                      | -                           | Optional title for the embedding task                       |
| `api_key`        | `str`                      | -                           | The API key used for authenticating requests.               |
| `request_params` | `Optional[Dict[str, Any]]` | -                           | Optional dictionary of parameters for the embedding request |
| `client_params`  | `Optional[Dict[str, Any]]` | -                           | Optional dictionary of parameters for the Gemini client     |
| `gemini_client`  | `Optional[Client]`         | -                           | Optional pre-configured Gemini client instance              |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/gemini_embedder.py)
