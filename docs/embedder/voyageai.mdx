---
title: Voyage AI Embedder
sidebarTitle: Voyage AI
---

The `VoyageAIEmbedder` class is used to embed text data into vectors using the Voyage AI API. Get your key from [here](https://dash.voyageai.com/api-keys).

## Usage

```python cookbook/embedders/voyageai_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.voyageai import VoyageAIEmbedder

# Embed sentence in database
embeddings = VoyageAIEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="voyageai_embeddings",
        embedder=VoyageAIEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter        | Type                       | Default                                    | Description                                                         |
| ---------------- | -------------------------- | ------------------------------------------ | ------------------------------------------------------------------- |
| `model`          | `str`                      | `"voyage-2"`                               | The name of the model used for generating embeddings.               |
| `dimensions`     | `int`                      | `1024`                                     | The dimensionality of the embeddings generated by the model.        |
| `request_params` | `Optional[Dict[str, Any]]` | -                                          | Additional parameters to include in the API request. Optional.      |
| `api_key`        | `str`                      | -                                          | The API key used for authenticating requests.                       |
| `base_url`       | `str`                      | `"https://api.voyageai.com/v1/embeddings"` | The base URL for the API endpoint.                                  |
| `max_retries`    | `Optional[int]`            | -                                          | The maximum number of retries for API requests. Optional.           |
| `timeout`        | `Optional[float]`          | -                                          | The timeout duration for API requests. Optional.                    |
| `client_params`  | `Optional[Dict[str, Any]]` | -                                          | Additional parameters for configuring the API client. Optional.     |
| `voyage_client`  | `Optional[Client]`         | -                                          | An instance of the Client to use for making API requests. Optional. |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/voyageai_embedder.py)
