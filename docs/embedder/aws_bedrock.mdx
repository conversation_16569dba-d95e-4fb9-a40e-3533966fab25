---
title: AWS Bedrock Embedder
sidebarTitle: AWS Bedrock
---

The `AwsBedrockEmbedder` class is used to embed text data into vectors using the AWS Bedrock API. By default, it uses the Cohere Embed Multilingual V3 model for generating embeddings.

# Setup

## Set your AWS credentials

```bash
export AWS_ACCESS_KEY_ID = xxx
export AWS_SECRET_ACCESS_KEY = xxx
export AWS_REGION = xxx
```

<Note>
By default, this embedder uses the `cohere.embed-multilingual-v3` model. You must enable access to this model from the AWS Bedrock model catalog before using this embedder.
</Note>

## Run PgVector
```bash
docker run - d \
    - e POSTGRES_DB = ai \
    - e POSTGRES_USER = ai \
    - e POSTGRES_PASSWORD = ai \
    - e PGDATA = /var/lib/postgresql/data/pgdata \
    - v pgvolume: / var/lib/postgresql/data \
    - p 5532: 5432 \
    - -name pgvector \
    agnohq/pgvector: 16
```

# Usage

```python cookbook/embedders/aws_bedrock_embedder.py

# Embed sentence in database
embeddings = AwsBedrockEmbedder().get_embedding(
    "The quick brown fox jumps over the lazy dog."
)
# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Example usage with a PDF knowledge base
knowledge_base = PDFUrlKnowledgeBase(
    urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
    reader=PDFUrlReader(
        chunk_size=2048
    ),  # Required because Cohere model has a fixed size of 2048
    vector_db=PgVector(
        table_name="recipes",
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        embedder=AwsBedrockEmbedder(),
    ),
)
knowledge_base.load(recreate=False)
```

# Params

| Parameter | Type | Default | Description |
| ----------------------- | ----------------------------- | ----------------------------- | ------------------------------------------------------------------------------------------ |
| `id` | `str` | `"cohere.embed-multilingual-v3"` | The model ID to use. You need to enable this model in your AWS Bedrock model catalog. |
| `dimensions` | `int` | `1024` | The dimensionality of the embeddings generated by the model(1024 for Cohere models). |
| `input_type` | `str` | `"search_query"` | Prepends special tokens to differentiate types. Options: 'search_document', 'search_query', 'classification', 'clustering'. |
| `truncate` | `Optional[str]` | `None` | How to handle inputs longer than the maximum token length. Options: 'NONE', 'START', 'END'. |
| `embedding_types` | `Optional[List[str]]` | `None` | Types of embeddings to return . Options: 'float', 'int8', 'uint8', 'binary', 'ubinary'. |
| `aws_region` | `Optional[str]` | `None` | The AWS region to use. If not provided, falls back to AWS_REGION env variable. |
| `aws_access_key_id` | `Optional[str]` | `None` | The AWS access key ID. If not provided, falls back to AWS_ACCESS_KEY_ID env variable. |
| `aws_secret_access_key` | `Optional[str]` | `None` | The AWS secret access key. If not provided, falls back to AWS_SECRET_ACCESS_KEY env variable. |
| `session` | `Optional[Session]` | `None` | A boto3 Session object to use for authentication. |
| `request_params` | `Optional[Dict[str, Any]]` | `None` | Additional parameters to pass to the API requests. |
| `client_params` | `Optional[Dict[str, Any]]` | `None` | Additional parameters to pass to the boto3 client. |
| `client` | `Optional[AwsClient]` | `None` | An instance of the AWS Bedrock client to use for making API requests. |

# Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/aws_bedrock_embedder.py)