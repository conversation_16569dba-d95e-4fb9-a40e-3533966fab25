---
title: Mistral Embedder
sidebarTitle: Mistral
---

The `MistralEmbedder` class is used to embed text data into vectors using the Mistral API. Get your key from [here](https://console.mistral.ai/api-keys/).

## Usage

```python cookbook/embedders/mistral_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.mistral import MistralEmbedder

# Embed sentence in database
embeddings = MistralEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="mistral_embeddings",
        embedder=MistralEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter        | Type                       | Default           | Description                                                                |
| ---------------- | -------------------------- | ----------------- | -------------------------------------------------------------------------- |
| `model`          | `str`                      | `"mistral-embed"` | The name of the model used for generating embeddings.                      |
| `dimensions`     | `int`                      | `1024`            | The dimensionality of the embeddings generated by the model.               |
| `request_params` | `Optional[Dict[str, Any]]` | -                 | Additional parameters to include in the API request. Optional.             |
| `api_key`        | `str`                      | -                 | The API key used for authenticating requests.                              |
| `endpoint`       | `str`                      | -                 | The endpoint URL for the API requests.                                     |
| `max_retries`    | `Optional[int]`            | -                 | The maximum number of retries for API requests. Optional.                  |
| `timeout`        | `Optional[int]`            | -                 | The timeout duration for API requests. Optional.                           |
| `client_params`  | `Optional[Dict[str, Any]]` | -                 | Additional parameters for configuring the API client. Optional.            |
| `mistral_client` | `Optional[MistralClient]`  | -                 | An instance of the MistralClient to use for making API requests. Optional. |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/mistral_embedder.py)
