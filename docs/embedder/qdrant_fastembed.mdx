---
title: Qdrant FastEmbed Embedder
sidebarTitle: FastEmbed
---

The `FastEmbedEmbedder` class is used to embed text data into vectors using the [FastEmbed](https://qdrant.github.io/fastembed/).

## Usage

```python cookbook/embedders/qdrant_fastembed.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.fastembed import FastEmbedEmbedder

# Embed sentence in database
embeddings = FastEmbedEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="qdrant_embeddings",
        embedder=FastEmbedEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter    | Type  | Default                  | Description                                    |
| ------------ | ----- | ------------------------ | ---------------------------------------------- |
| `dimensions` | `int` | -                        | The dimensionality of the generated embeddings |
| `model`      | `str` | `BAAI/bge-small-en-v1.5` | The name of the qdrant_fastembed model to use  |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/qdrant_fastembed.py)
