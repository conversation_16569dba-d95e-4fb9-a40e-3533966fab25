---
title: Azure OpenAI Embedder
sidebarTitle: Azure OpenAI
---

The `AzureOpenAIEmbedder` class is used to embed text data into vectors using the Azure OpenAI API. Get your key from [here](https://ai.azure.com/).

## Setup

### Set your API keys

```bash
export AZURE_EMBEDDER_OPENAI_API_KEY=xxx
export AZURE_EMBEDDER_OPENAI_ENDPOINT=xxx
export AZURE_EMBEDDER_DEPLOYMENT=xxx
```

### Run PgVector
```bash
docker run -d \
  -e POSTGRES_DB=ai \
  -e POSTGRES_USER=ai \
  -e POSTGRES_PASSWORD=ai \
  -e PGDATA=/var/lib/postgresql/data/pgdata \
  -v pgvolume:/var/lib/postgresql/data \
  -p 5532:5432 \
  --name pgvector \
  agnohq/pgvector:16
```

## Usage

```python cookbook/embedders/azure_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.azure_openai import AzureOpenAIEmbedder

# Embed sentence in database
embeddings = AzureOpenAIEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="azure_openai_embeddings",
        embedder=AzureOpenAIEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter                 | Type                          | Default                    | Description                                                                      |
| ------------------------- | ----------------------------- | -------------------------- | -------------------------------------------------------------------------------- |
| `model`                   | `str`                         | `"text-embedding-ada-002"` | The name of the model used for generating embeddings.                            |
| `dimensions`              | `int`                         | `1536`                     | The dimensionality of the embeddings generated by the model.                     |
| `encoding_format`         | `Literal['float', 'base64']`  | `"float"`                  | The format in which the embeddings are encoded. Options are "float" or "base64". |
| `user`                    | `str`                         | -                          | The user associated with the API request.                                        |
| `api_key`                 | `str`                         | -                          | The API key used for authenticating requests.                                    |
| `api_version`             | `str`                         | `"2024-02-01"`             | The version of the API to use for the requests.                                  |
| `azure_endpoint`          | `str`                         | -                          | The Azure endpoint for the API requests.                                         |
| `azure_deployment`        | `str`                         | -                          | The Azure deployment name for the API requests.                                  |
| `base_url`                | `str`                         | -                          | The base URL for the API endpoint.                                               |
| `azure_ad_token`          | `str`                         | -                          | The Azure Active Directory token for authentication.                             |
| `azure_ad_token_provider` | `Any`                         | -                          | The provider for obtaining the Azure AD token.                                   |
| `organization`            | `str`                         | -                          | The organization associated with the API request.                                |
| `request_params`          | `Optional[Dict[str, Any]]`    | -                          | Additional parameters to include in the API request. Optional.                   |
| `client_params`           | `Optional[Dict[str, Any]]`    | -                          | Additional parameters for configuring the API client. Optional.                  |
| `openai_client`           | `Optional[AzureOpenAIClient]` | -                          | An instance of the AzureOpenAIClient to use for making API requests. Optional.   |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/azure_embedder.py)
