---
title: Fireworks Embedder
sidebarTitle: Fireworks
---

The `FireworksEmbedder` can be used to embed text data into vectors using the Fireworks API. Fireworks uses the OpenAI API specification, so the `FireworksEmbedder` class is similar to the `OpenAIEmbedder` class, incorporating adjustments to ensure compatibility with the Fireworks platform. Get your key from [here](https://fireworks.ai/account/api-keys).

## Usage

```python cookbook/embedders/fireworks_embedder.py
from agno.agent import AgentKnowledge
from agno.vectordb.pgvector import PgVector
from agno.embedder.fireworks import FireworksEmbedder

# Embed sentence in database
embeddings = FireworksEmbedder().get_embedding("The quick brown fox jumps over the lazy dog.")

# Print the embeddings and their dimensions
print(f"Embeddings: {embeddings[:5]}")
print(f"Dimensions: {len(embeddings)}")

# Use an embedder in a knowledge base
knowledge_base = AgentKnowledge(
    vector_db=PgVector(
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",
        table_name="fireworks_embeddings",
        embedder=FireworksEmbedder(),
    ),
    num_documents=2,
)
```

## Params

| Parameter    | Type  | Default                                   | Description                                                  |
| ------------ | ----- | ----------------------------------------- | ------------------------------------------------------------ |
| `model`      | `str` | `"nomic-ai/nomic-embed-text-v1.5"`        | The name of the model used for generating embeddings.        |
| `dimensions` | `int` | `768`                                     | The dimensionality of the embeddings generated by the model. |
| `api_key`    | `str` | -                                         | The API key used for authenticating requests.                |
| `base_url`   | `str` | `"https://api.fireworks.ai/inference/v1"` | The base URL for the API endpoint.                           |

## Developer Resources
- View [Cookbook](https://github.com/agno-agi/agno/blob/main/cookbook/agent_concepts/knowledge/embedders/fireworks_embedder.py)
