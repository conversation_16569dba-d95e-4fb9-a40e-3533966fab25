# 🇦🇪 Rydo - UAE Place Discovery Agent

Rydo is a specialized conversational AI agent designed to help users discover the best places across the United Arab Emirates. Unlike generic location services, Rydo provides personalized, curated recommendations through interactive chat.

## ✨ Features

- **🇦🇪 UAE-Focused**: Exclusively covers all seven emirates (Dubai, Abu Dhabi, Sharjah, Ajman, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)
- **💬 Conversational Interface**: Interactive chat that refines recommendations based on your preferences
- **🎯 Smart Filtering**: Understands context like budget, cuisine, ambiance, and special requirements
- **⭐ Top 5 Results**: Always provides exactly 5 curated recommendations ranked by quality
- **📍 Detailed Information**: Includes ratings, reviews, contact details, and location specifics
- **🔄 Iterative Refinement**: Continues conversation to perfect recommendations

## 🚀 Quick Start

### Prerequisites

1. **API Keys Required:**
   ```bash
   export GOOGLE_MAPS_API_KEY="your_google_maps_api_key"
   export OPENAI_API_KEY="your_openai_api_key"
   ```

2. **Dependencies:**
   ```bash
   pip install agno googlemaps google-maps-places openai
   ```

### Running Rydo

#### Interactive Mode (Full Experience)
```bash
python cookbook/tools/rydo_agent.py
```

#### Demo Mode (Quick Preview)
```bash
python cookbook/tools/rydo_agent.py demo
```

## 💬 How to Use Rydo

### 1. Start with a Greeting
Rydo will welcome you and explain what it can help with.

### 2. Tell Rydo What You're Looking For
Examples:
- "I want to find great restaurants in Dubai"
- "Looking for family-friendly cafes in Abu Dhabi"
- "Need a good place for a business lunch in Sharjah"
- "Want to discover hidden gems in Ras Al Khaimah"

### 3. Refine Through Conversation
Rydo will ask follow-up questions to understand your preferences:
- **Location**: Which emirate or specific area?
- **Type**: Restaurant, cafe, shopping, entertainment, etc.
- **Budget**: Budget-friendly, mid-range, or luxury?
- **Cuisine**: Arabic, International, Asian, etc.
- **Ambiance**: Casual, formal, family-friendly, romantic, etc.
- **Special Needs**: Parking, accessibility, outdoor seating, etc.

### 4. Get Your Top 5 Recommendations
Each recommendation includes:
- Name and description
- Exact location (area and emirate)
- Rating and highlights
- Contact information
- Why it's special

## 🎯 What Rydo Can Help You Find

### 🍽️ **Food & Dining**
- Restaurants (all cuisines)
- Cafes and coffee shops
- Street food and local eateries
- Fine dining establishments
- Breakfast and brunch spots

### 🛍️ **Shopping**
- Shopping malls
- Traditional souks
- Boutique stores
- Local markets
- Specialty shops

### 🎪 **Entertainment**
- Theme parks and attractions
- Cinemas and theaters
- Gaming centers
- Family entertainment
- Nightlife venues

### 🏨 **Accommodation**
- Luxury hotels
- Budget-friendly stays
- Resorts and spas
- Business hotels
- Unique accommodations

### 🎨 **Culture & Heritage**
- Museums and galleries
- Historic sites
- Cultural centers
- Art exhibitions
- Heritage villages

### 🌊 **Outdoor & Adventure**
- Beaches and beach clubs
- Parks and gardens
- Adventure sports
- Desert experiences
- Water activities

### 💼 **Business & Services**
- Co-working spaces
- Meeting venues
- Salons and spas
- Fitness centers
- Professional services

## 🗣️ Example Conversations

### Finding Restaurants
```
You: "Hi Rydo, I'm looking for good restaurants in Dubai"

Rydo: "Hello! I'd love to help you find amazing restaurants in Dubai! 
To give you the best recommendations, could you tell me:
- What type of cuisine are you in the mood for?
- Which area of Dubai would you prefer?
- What's your budget range?
- Any special preferences (outdoor seating, family-friendly, etc.)?"

You: "I want Arabic food, somewhere in Dubai Marina, mid-range budget"

Rydo: [Provides 5 top-rated Arabic restaurants in Dubai Marina with details]
```

### Discovering Cafes
```
You: "I need a quiet cafe in Abu Dhabi for a business meeting"

Rydo: "Perfect! I can help you find the ideal business-friendly cafe in Abu Dhabi.
A few questions to ensure the perfect match:
- Which area of Abu Dhabi works best for you?
- Do you need WiFi and power outlets?
- Any preference for indoor vs outdoor seating?
- What time of day will you be meeting?"

You: "Somewhere in Corniche area, need good WiFi, indoor seating preferred"

Rydo: [Provides 5 business-friendly cafes in Corniche area with amenities]
```

## 🔧 Technical Details

### Architecture
- **Base**: Agno Agent framework
- **Tools**: Google Maps Places API (search_places, geocode_address)
- **LLM**: OpenAI GPT models
- **Focus**: UAE-specific location intelligence

### API Usage
Rydo uses minimal Google Maps API calls:
- `search_places`: Main functionality for finding places
- `geocode_address`: For location validation and coordinates

### Optimizations
- Streamlined tool selection (only essential APIs)
- UAE-focused queries for better results
- Conversation memory for context retention
- Smart filtering based on user preferences

## 🛠️ Customization

### Modifying Search Preferences
Edit the agent instructions in `rydo_agent.py` to adjust:
- Default search parameters
- Conversation flow
- Response formatting
- UAE-specific knowledge

### Adding New Categories
Extend the place types in the instructions section to include:
- New business categories
- Seasonal recommendations
- Event-based suggestions
- Local specialties

## 🔒 Security & Privacy

- API keys are loaded from environment variables
- No user data is stored permanently
- Conversation context is session-based only
- Google Maps API calls follow standard privacy policies

## 📞 Support

For issues or questions:
1. Check API key configuration
2. Verify Google Maps APIs are enabled
3. Ensure UAE location queries are being used
4. Review conversation flow for context

## 🎉 Why Rydo?

Rydo isn't just another location search tool. It's your personal UAE discovery companion that:
- **Understands Context**: Knows the difference between a business lunch spot and a family dinner place
- **Learns Your Preferences**: Remembers what you're looking for throughout the conversation
- **Provides Quality**: Always gives you the top 5 options, not overwhelming lists
- **Knows the UAE**: Specialized knowledge of local culture, areas, and preferences
- **Saves Time**: No need to browse through hundreds of options - get curated recommendations

**Start discovering the best of the UAE with Rydo today! 🇦🇪✨**
