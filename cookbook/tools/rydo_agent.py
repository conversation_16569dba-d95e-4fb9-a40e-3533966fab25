"""
Rydo - UAE Place Discovery Agent

A conversational agent specialized in helping users find the best places in the UAE.
Rydo focuses on providing personalized recommendations through interactive chat,
utilizing Google Maps/Places API to deliver top-quality results.

Prerequisites:
- Set the environment variable `GOOGLE_MAPS_API_KEY` with your Google Maps API key
- Set the environment variable `OPENAI_API_KEY` with your OpenAI API key

Features:
- UAE-focused place discovery
- Interactive conversational interface
- Smart filtering and refinement through chat
- Top 5 curated results
- Detailed place information including ratings, reviews, and contact details
"""

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.google_maps import GoogleMapTools


class RydoAgent:
    def __init__(self):
        """Initialize Rydo agent with UAE-focused configuration."""
        
        # Create agent with minimal Google Maps tools (only what we need)
        self.agent = Agent(
            name="Rydo",
            model=OpenAIChat(id="gpt-4o-mini"),  # Use GPT-4o-mini for cost efficiency
            tools=[
                GoogleMapTools(
                    search_places=True,      # Main functionality for place search
                    geocode_address=True,    # For location validation
                    get_directions=False,    # Not needed for place discovery
                    validate_address=False,  # Not needed
                    reverse_geocode=False,   # Not needed
                    get_distance_matrix=False,  # Not needed
                    get_elevation=False,     # Not needed
                    get_timezone=False,      # Not needed
                )
            ],
            description="""You are Rydo, a friendly and knowledgeable UAE place discovery assistant. 
            You specialize in helping people find the best places across the United Arab Emirates, 
            including Dubai, Abu Dhabi, Sharjah, Ajman, Ras Al Khaimah, Fujairah, and Umm Al Quwain.""",
            
            instructions=[
                "🇦🇪 You are Rydo, the UAE's premier place discovery assistant",
                "Always greet users warmly and ask how you can help them discover amazing places in the UAE",
                "Focus EXCLUSIVELY on locations within the United Arab Emirates",
                "When users ask about places, engage in conversation to understand their preferences:",
                "  - What type of place they're looking for (restaurants, cafes, shopping, entertainment, etc.)",
                "  - Which emirate or city they prefer (Dubai, Abu Dhabi, Sharjah, etc.)",
                "  - Their budget range (budget-friendly, mid-range, luxury)",
                "  - Specific preferences (cuisine type, ambiance, family-friendly, etc.)",
                "  - Any special requirements (parking, accessibility, outdoor seating, etc.)",
                "Use the search_places tool with UAE-specific queries like 'best restaurants in Dubai Marina' or 'family cafes in Abu Dhabi'",
                "Always provide exactly 5 top recommendations, ranked by rating and relevance",
                "For each recommendation, include:",
                "  - Name and brief description",
                "  - Location (area/district and emirate)",
                "  - Rating and key highlights",
                "  - Contact information if available",
                "  - Why it's special or recommended",
                "Format responses in a friendly, conversational tone with emojis",
                "If users ask about places outside the UAE, politely redirect them to UAE locations",
                "Always end with asking if they'd like more specific recommendations or have other preferences",
            ],
            
            markdown=True,
            show_tool_calls=True,
        )
    
    def greet(self):
        """Initial greeting when starting the agent."""
        greeting = """
# 🇦🇪 Welcome to Rydo! 

Hello! I'm Rydo, your personal UAE place discovery assistant. I'm here to help you find the most amazing places across the beautiful United Arab Emirates! ✨

## What I can help you with:

🍽️ **Restaurants & Cafes** - From street food to fine dining
🛍️ **Shopping** - Malls, souks, and unique boutiques  
🎯 **Entertainment** - Theme parks, cinemas, and fun activities
🏨 **Hotels & Stays** - Luxury resorts to budget-friendly options
🎨 **Culture & Heritage** - Museums, galleries, and historic sites
🌊 **Outdoor & Adventure** - Beaches, parks, and adventure sports
💼 **Business & Services** - Co-working spaces, salons, and more

## How it works:
1. Tell me what type of place you're looking for
2. I'll ask a few questions to understand your preferences
3. You'll get my top 5 personalized recommendations with all the details you need!

**What kind of place are you looking to discover in the UAE today?** 🤔
        """
        print(greeting)
        return greeting
    
    def chat(self, user_input: str):
        """Process user input and provide conversational response."""
        return self.agent.print_response(user_input, stream=True)
    
    def interactive_session(self):
        """Start an interactive chat session with Rydo."""
        self.greet()
        
        print("\n" + "="*60)
        print("💬 Start chatting with Rydo! (Type 'quit' to exit)")
        print("="*60 + "\n")
        
        while True:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                    print("\n🇦🇪 Rydo: Thank you for using Rydo! Hope you discover amazing places in the UAE! 👋")
                    break
                
                if not user_input:
                    continue
                
                print(f"\n🇦🇪 Rydo:")
                self.chat(user_input)
                print("\n" + "-"*60 + "\n")
                
            except KeyboardInterrupt:
                print("\n\n🇦🇪 Rydo: Goodbye! Have a wonderful time exploring the UAE! 👋")
                break
            except Exception as e:
                print(f"\n❌ Sorry, I encountered an error: {e}")
                print("Let's try again! What would you like to discover in the UAE?")


def demo():
    """Demo function showing Rydo's capabilities."""
    print("🚀 Rydo Demo - UAE Place Discovery Agent")
    print("="*50)

    try:
        rydo = RydoAgent()

        # Demo conversation flow
        demo_queries = [
            "Hi Rydo! What can you help me with?",
            "I'm looking for the best restaurants in Dubai",
            "I want something family-friendly with good Arabic food in Dubai Marina",
            "Can you also suggest some nice cafes in Abu Dhabi for a business meeting?",
        ]

        for i, query in enumerate(demo_queries, 1):
            print(f"\n{'='*60}")
            print(f"Demo Query {i}: {query}")
            print('='*60)
            rydo.chat(query)

    except Exception as e:
        print(f"❌ Error running Rydo demo: {e}")
        print("\n💡 Make sure you have set your API keys:")
        print("   export GOOGLE_MAPS_API_KEY='your_google_maps_api_key'")
        print("   export OPENAI_API_KEY='your_openai_api_key'")


def main():
    """Main function to run Rydo agent."""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo()
    else:
        print("🚀 Starting Rydo - UAE Place Discovery Agent...")

        try:
            rydo = RydoAgent()
            rydo.interactive_session()
        except Exception as e:
            print(f"❌ Error starting Rydo: {e}")
            print("\n💡 Make sure you have set your API keys:")
            print("   export GOOGLE_MAPS_API_KEY='your_google_maps_api_key'")
            print("   export OPENAI_API_KEY='your_openai_api_key'")


if __name__ == "__main__":
    main()
