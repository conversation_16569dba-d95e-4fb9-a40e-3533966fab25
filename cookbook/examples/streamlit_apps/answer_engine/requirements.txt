# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.6.0
    # via -r cookbook/examples/apps/answer_engine/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via -r cookbook/examples/apps/answer_engine/requirements.in
anyio==4.8.0
    # via
    #   anthropic
    #   groq
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   duckduckgo-search
    #   streamlit
    #   typer
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
docstring-parser==0.16
    # via agno
duckduckgo-search==7.5.2
    # via -r cookbook/examples/apps/answer_engine/requirements.in
exa-py==1.9.0
    # via -r cookbook/examples/apps/answer_engine/requirements.in
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.24.2
    # via
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.164.0
    # via google-generativeai
google-auth==2.38.0
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-generativeai==0.8.4
    # via -r cookbook/examples/apps/answer_engine/requirements.in
google-search-results==2.4.2
    # via -r cookbook/examples/apps/answer_engine/requirements.in
googleapis-common-protos==1.69.1
    # via
    #   google-api-core
    #   grpcio-status
groq==0.19.0
    # via -r cookbook/examples/apps/answer_engine/requirements.in
grpcio==1.71.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   groq
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.9.0
    # via
    #   anthropic
    #   openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
lxml==5.3.1
    # via duckduckgo-search
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
narwhals==1.30.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/answer_engine/requirements.in
numpy==2.2.3
    # via
    #   pandas
    #   pydeck
    #   streamlit
openai==1.66.3
    # via
    #   -r cookbook/examples/apps/answer_engine/requirements.in
    #   exa-py
packaging==24.2
    # via
    #   altair
    #   streamlit
pandas==2.2.3
    # via streamlit
pillow==11.1.0
    # via streamlit
primp==0.14.0
    # via duckduckgo-search
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.3
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
    #   streamlit
pyarrow==19.0.1
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pydantic==2.10.6
    # via
    #   agno
    #   anthropic
    #   google-generativeai
    #   groq
    #   openai
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.8.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pyparsing==3.2.1
    # via httplib2
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.1
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   exa-py
    #   google-api-core
    #   google-search-results
    #   streamlit
rich==13.9.4
    # via
    #   agno
    #   typer
rpds-py==0.23.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
sqlalchemy==2.0.39
    # via -r cookbook/examples/apps/answer_engine/requirements.in
streamlit==1.43.2
    # via -r cookbook/examples/apps/answer_engine/requirements.in
tenacity==9.0.0
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   google-generativeai
    #   openai
typer==0.15.2
    # via agno
typing-extensions==4.12.2
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   exa-py
    #   google-generativeai
    #   groq
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
tzdata==2025.1
    # via pandas
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.3.0
    # via requests
