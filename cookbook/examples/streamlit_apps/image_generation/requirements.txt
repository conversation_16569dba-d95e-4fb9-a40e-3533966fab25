# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.6.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   groq
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via streamlit
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via
    #   streamlit
    #   typer
cohere==5.15.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
coloredlogs==15.0.1
    # via onnxruntime
distro==1.9.0
    # via
    #   groq
    #   openai
docstring-parser==0.16
    # via agno
fastavro==1.10.0
    # via cohere
filelock==3.18.0
    # via huggingface-hub
flatbuffers==25.2.10
    # via onnxruntime
fsspec==2025.3.2
    # via huggingface-hub
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
groq==0.24.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
h11==0.16.0
    # via httpcore
hf-xet==1.1.0
    # via huggingface-hub
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   -r cookbook/examples/apps/image_generation/requirements.in
    #   agno
    #   cohere
    #   groq
    #   openai
httpx-sse==0.4.0
    # via cohere
huggingface-hub==0.31.1
    # via tokenizers
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.9.0
    # via openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
mpmath==1.3.0
    # via sympy
narwhals==1.38.2
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
numpy==2.2.5
    # via
    #   onnxruntime
    #   opencv-python
    #   pandas
    #   pgvector
    #   pydeck
    #   rapidocr-onnxruntime
    #   shapely
    #   streamlit
onnxruntime==1.22.0
    # via rapidocr-onnxruntime
openai==1.78.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
opencv-python==4.11.0.86
    # via rapidocr-onnxruntime
packaging==24.2
    # via
    #   altair
    #   huggingface-hub
    #   onnxruntime
    #   streamlit
pandas==2.2.3
    # via streamlit
pgvector==0.4.1
    # via -r cookbook/examples/apps/image_generation/requirements.in
pillow==11.2.1
    # via
    #   rapidocr-onnxruntime
    #   streamlit
protobuf==6.30.2
    # via
    #   onnxruntime
    #   streamlit
psycopg==3.2.7
    # via -r cookbook/examples/apps/image_generation/requirements.in
psycopg-binary==3.2.7
    # via psycopg
pyarrow==20.0.0
    # via streamlit
pyclipper==1.3.0.post6
    # via rapidocr-onnxruntime
pydantic==2.11.4
    # via
    #   agno
    #   cohere
    #   groq
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via
    #   cohere
    #   pydantic
pydantic-settings==2.9.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pypdf==5.4.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.0
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   agno
    #   huggingface-hub
    #   rapidocr-onnxruntime
rapidocr-onnxruntime==1.4.4
    # via -r cookbook/examples/apps/image_generation/requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   cohere
    #   huggingface-hub
    #   streamlit
rich==14.0.0
    # via
    #   agno
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
shapely==2.1.0
    # via rapidocr-onnxruntime
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   python-dateutil
    #   rapidocr-onnxruntime
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   groq
    #   openai
sqlalchemy==2.0.40
    # via -r cookbook/examples/apps/image_generation/requirements.in
streamlit==1.45.0
    # via -r cookbook/examples/apps/image_generation/requirements.in
sympy==1.14.0
    # via onnxruntime
tenacity==9.1.2
    # via streamlit
tokenizers==0.21.1
    # via cohere
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   openai
    #   rapidocr-onnxruntime
typer==0.15.3
    # via agno
types-requests==2.32.0.20250328
    # via cohere
typing-extensions==4.13.2
    # via
    #   agno
    #   altair
    #   anyio
    #   cohere
    #   groq
    #   huggingface-hub
    #   openai
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via
    #   requests
    #   types-requests
