# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.6.0
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   google-genai
    #   httpx
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
beautifulsoup4==4.13.4
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   googlesearch-python
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   streamlit
    #   typer
contourpy==1.3.2
    # via matplotlib
cycler==0.12.1
    # via matplotlib
docstring-parser==0.16
    # via agno
fonttools==4.57.0
    # via matplotlib
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-auth==2.39.0
    # via google-genai
google-genai==1.11.0
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
googlesearch-python==1.3.0
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
h11==0.14.0
    # via httpcore
httpcore==1.0.8
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   google-genai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
kiwisolver==1.4.8
    # via matplotlib
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
matplotlib==3.10.1
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
mdurl==0.1.2
    # via markdown-it-py
narwhals==1.35.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
numpy==2.2.4
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   contourpy
    #   matplotlib
    #   pandas
    #   pydeck
    #   streamlit
packaging==24.2
    # via
    #   altair
    #   matplotlib
    #   streamlit
pandas==2.2.3
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   streamlit
pillow==11.2.1
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   matplotlib
    #   streamlit
protobuf==5.29.4
    # via streamlit
pyarrow==19.0.1
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycountry==24.6.1
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
pydantic==2.11.3
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   agno
    #   google-genai
    #   pydantic-settings
pydantic-core==2.33.1
    # via pydantic
pydantic-settings==2.8.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pyparsing==3.2.3
    # via matplotlib
python-dateutil==2.9.0.post0
    # via
    #   matplotlib
    #   pandas
python-dotenv==1.1.0
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   google-genai
    #   googlesearch-python
    #   streamlit
rich==14.0.0
    # via
    #   agno
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via anyio
soupsieve==2.6
    # via beautifulsoup4
streamlit==1.44.1
    # via -r cookbook/examples/apps/gemini-tutor/requirements.in
tenacity==9.1.2
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
typer==0.15.2
    # via agno
typing-extensions==4.13.2
    # via
    #   -r cookbook/examples/apps/gemini-tutor/requirements.in
    #   agno
    #   altair
    #   anyio
    #   beautifulsoup4
    #   google-genai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
websockets==15.0.1
    # via google-genai
