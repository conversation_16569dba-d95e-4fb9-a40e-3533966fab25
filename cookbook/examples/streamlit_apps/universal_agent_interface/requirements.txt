# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.6.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
aiofiles==24.1.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.49.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
anyio==4.9.0
    # via
    #   anthropic
    #   google-genai
    #   groq
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
beautifulsoup4==4.13.3
    # via
    #   -r cookbook/examples/apps/universal_agent_interface/requirements.in
    #   yfinance
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   duckduckgo-search
    #   streamlit
    #   typer
deprecation==2.1.0
    # via lancedb
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
docstring-parser==0.16
    # via agno
duckdb==1.2.2
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
duckduckgo-search==8.0.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
exa-py==1.12.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
frozendict==2.4.6
    # via yfinance
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-auth==2.38.0
    # via google-genai
google-genai==1.10.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
groq==0.22.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
grpcio==1.71.0
    # via
    #   grpcio-tools
    #   qdrant-client
grpcio-tools==1.71.0
    # via qdrant-client
h11==0.14.0
    # via httpcore
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.8
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   exa-py
    #   google-genai
    #   groq
    #   openai
    #   qdrant-client
hyperframe==6.1.0
    # via h2
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.9.0
    # via
    #   anthropic
    #   openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
lancedb==0.21.2
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
lxml==5.3.2
    # via
    #   duckduckgo-search
    #   python-docx
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multitasking==0.0.11
    # via yfinance
narwhals==1.34.1
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
numpy==2.2.4
    # via
    #   pandas
    #   pydeck
    #   qdrant-client
    #   streamlit
    #   yfinance
openai==1.73.0
    # via
    #   -r cookbook/examples/apps/universal_agent_interface/requirements.in
    #   exa-py
overrides==7.7.0
    # via lancedb
packaging==24.2
    # via
    #   altair
    #   deprecation
    #   lancedb
    #   pytest
    #   streamlit
pandas==2.2.3
    # via
    #   streamlit
    #   yfinance
peewee==3.17.9
    # via yfinance
pillow==11.2.1
    # via streamlit
platformdirs==4.3.7
    # via yfinance
pluggy==1.5.0
    # via pytest
portalocker==2.10.1
    # via qdrant-client
primp==0.14.0
    # via duckduckgo-search
protobuf==5.29.4
    # via
    #   grpcio-tools
    #   streamlit
pyarrow==19.0.1
    # via
    #   lancedb
    #   streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.3
    # via
    #   agno
    #   anthropic
    #   exa-py
    #   google-genai
    #   groq
    #   lancedb
    #   openai
    #   pydantic-settings
    #   qdrant-client
pydantic-core==2.33.1
    # via pydantic
pydantic-settings==2.8.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pypdf==5.4.0
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
pytest==8.3.5
    # via pytest-mock
pytest-mock==3.14.0
    # via exa-py
python-dateutil==2.9.0.post0
    # via pandas
python-docx==1.1.2
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
python-dotenv==1.1.0
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via
    #   pandas
    #   yfinance
pyyaml==6.0.2
    # via agno
qdrant-client==1.13.3
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   exa-py
    #   google-genai
    #   streamlit
    #   yfinance
rich==14.0.0
    # via
    #   agno
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
setuptools==78.1.0
    # via grpcio-tools
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy==2.0.40
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
streamlit==1.44.1
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
tantivy==0.22.2
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
tenacity==9.1.2
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   lancedb
    #   openai
typer==0.15.2
    # via agno
typing-extensions==4.13.2
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   beautifulsoup4
    #   exa-py
    #   google-genai
    #   groq
    #   openai
    #   pydantic
    #   pydantic-core
    #   python-docx
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via
    #   qdrant-client
    #   requests
websockets==15.0.1
    # via google-genai
yfinance==0.2.55
    # via -r cookbook/examples/apps/universal_agent_interface/requirements.in
