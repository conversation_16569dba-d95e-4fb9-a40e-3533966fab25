# Google Maps Agent Setup Guide

This guide will help you set up and run the Google Maps/Places agent from the Agno cookbook on any device.

## Prerequisites

- Python 3.9 or higher
- Google Cloud account
- OpenAI account

## Step 1: Clone and Setup Project

```bash
# Clone the repository
git clone https://github.com/agno-agi/agno.git
cd agno

# Create and activate virtual environment
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

## Step 2: Install Dependencies

```bash
# Install required packages
pip install -U openai googlemaps agno google-maps-places crawl4ai
```

## Step 3: Get API Keys

### Google Maps API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/projectselector2/google/maps-apis/credentials)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Places API (New)
   - Geocoding API
   - Address Validation API
   - Distance Matrix API
   - Elevation API
   - Time Zone API
   - Maps JavaScript API (optional)
4. Create credentials → API Key
5. Copy your API key

### OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Copy your API key

## Step 4: Install Google Cloud CLI

### macOS
```bash
# Using Homebrew
brew install google-cloud-sdk

# Or download from: https://cloud.google.com/sdk/docs/install
```

### Windows
```bash
# Download and install from: https://cloud.google.com/sdk/docs/install-sdk#windows
```

### Linux
```bash
# Download and install from: https://cloud.google.com/sdk/docs/install-sdk#linux
```

## Step 5: Authenticate with Google Cloud

```bash
# Authenticate with your Google account
gcloud auth login

# Set up application default credentials
gcloud auth application-default login

# Set your project (replace with your project ID)
gcloud config set project YOUR_PROJECT_ID
```

## Step 6: Enable Required APIs

```bash
# Enable Google Maps APIs
gcloud services enable places.googleapis.com
gcloud services enable maps-backend.googleapis.com
gcloud services enable geocoding-backend.googleapis.com
gcloud services enable addressvalidation.googleapis.com
```

## Step 7: Set Environment Variables

```bash
# Set your API keys (replace with your actual keys)
export GOOGLE_MAPS_API_KEY="your_google_maps_api_key_here"
export OPENAI_API_KEY="your_openai_api_key_here"
```

### For Windows (Command Prompt)
```cmd
set GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
set OPENAI_API_KEY=your_openai_api_key_here
```

### For Windows (PowerShell)
```powershell
$env:GOOGLE_MAPS_API_KEY="your_google_maps_api_key_here"
$env:OPENAI_API_KEY="your_openai_api_key_here"
```

## Step 8: Run the Agent

```bash
# Make sure virtual environment is activated
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Run the Google Maps agent
python cookbook/tools/google_maps_tools.py
```

## What the Agent Can Do

The Google Maps agent includes 10 different examples:

1. **Business Search** - Find restaurants, shops, services with ratings
2. **Directions** - Get driving directions between locations
3. **Address Validation** - Validate and standardize addresses
4. **Distance Matrix** - Calculate travel times between multiple locations
5. **Nearby Places** - Find coffee shops, museums, etc. near locations
6. **Reverse Geocoding** - Get address from coordinates
7. **Multi-step Route Planning** - Plan routes with multiple stops
8. **Location Analysis** - Get detailed location information
9. **Business Hours** - Find businesses with specific criteria
10. **Transit Options** - Compare driving, walking, and transit

## Testing Individual Features

You can test specific features by creating a simple script:

```python
from agno.agent import Agent
from agno.tools.google_maps import GoogleMapTools

# Create agent with specific tools
agent = Agent(
    name="Maps Test Agent",
    tools=[GoogleMapTools(
        search_places=True,      # Enable place search
        get_directions=True,     # Enable directions
        geocode_address=True,    # Enable geocoding
        # ... other features
    )],
    show_tool_calls=True
)

# Test place search
agent.print_response("Find coffee shops in San Francisco", stream=True)
```

## Troubleshooting

### Authentication Issues
- Make sure you've run `gcloud auth application-default login`
- Check that your project has the required APIs enabled
- Verify your API keys are correct

### API Quota Issues
- Check your Google Cloud Console for API usage limits
- Some APIs have daily quotas that may be exceeded

### Legacy API Errors
If you see "legacy API" errors for Directions or Distance Matrix:
1. Go to Google Cloud Console
2. Enable the legacy versions of these APIs
3. Or use the new Routes API instead

### Permission Errors
- Ensure your Google Cloud project has billing enabled
- Check that your API keys have the correct permissions

## Environment Variables (Alternative)

Instead of setting environment variables each time, you can create a `.env` file:

```bash
# Create .env file in project root
echo "GOOGLE_MAPS_API_KEY=your_key_here" > .env
echo "OPENAI_API_KEY=your_key_here" >> .env
```

Then load it in your Python script:
```python
from dotenv import load_dotenv
load_dotenv()
```

## Security Notes

- Never commit API keys to version control
- Use environment variables or secure secret management
- Restrict API key usage to specific IPs/domains in production
- Monitor API usage in Google Cloud Console

## Support

- [Agno Documentation](https://docs.agno.ai)
- [Google Maps Platform Documentation](https://developers.google.com/maps)
- [OpenAI API Documentation](https://platform.openai.com/docs)

---

**Note**: This setup enables the full Google Maps agent functionality including Places API, geocoding, and other location services. Some features like Directions may require additional legacy API enablement.
