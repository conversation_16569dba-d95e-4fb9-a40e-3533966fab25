#!/usr/bin/env python3
"""
Simple test script to verify Google Maps API functionality
"""

import os
from agno.agent import Agent
from agno.tools.google_maps import GoogleMapTools

# Set API keys
os.environ["GOOGLE_MAPS_API_KEY"] = "AIzaSyCnRgTAYen8Mt-VbMGWWDGzsgzDX3VgCsI"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Create agent with only the working Google Maps functions (excluding search_places)
agent = Agent(
    name="Maps API Test Agent",
    tools=[
        GoogleMapTools(
            search_places=False,  # Disable the problematic Places API function
            get_directions=True,
            geocode_address=True,
            reverse_geocode=True,
            get_distance_matrix=True,
            get_elevation=True,
            get_timezone=True,
        )
    ],
    description="Test agent for Google Maps API functionality",
    markdown=True,
    show_tool_calls=True,
)

print("=== Testing Geocoding ===")
agent.print_response(
    "What are the coordinates for 1600 Amphitheatre Parkway, Mountain View, CA?",
    stream=True,
)

print("\n=== Testing Directions ===")
agent.print_response(
    "Get driving directions from San Francisco to San Jose",
    stream=True,
)
